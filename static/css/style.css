:root {
  --primary-color: #4a6bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --warning-color: #ffc107;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --border-radius: 10px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Base styles with mobile-first approach */
html {
  touch-action: manipulation; /* Improves touch responsiveness */
  height: 100%;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: #333;
  min-height: 100%;
  -webkit-tap-highlight-color: transparent; /* Removes tap highlight on iOS */
  overflow-x: hidden; /* Prevent horizontal scroll */
}

/* Improved text readability on mobile */
p, .card-text, .table td, .form-label {
  font-size: 0.95rem;
  line-height: 1.5;
}

h1, h2, h3, h4, h5, h6 {
  line-height: 1.3;
}

/* Navbar styling - mobile first */
.navbar {
  background: linear-gradient(135deg, var(--primary-color), #6a5acd);
  box-shadow: var(--box-shadow);
  padding: 0.75rem 0;
  position: sticky;
  top: 0;
  z-index: 1020;
}

.navbar-brand {
  font-weight: 600;
  font-size: 1.25rem;
}

/* Navbar button styles */
.navbar .btn-outline-light {
  border-color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

.navbar .btn-outline-light:hover, 
.navbar .btn-outline-light:active {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Touch-friendly navbar buttons */
.navbar .btn {
  min-height: 38px; /* Minimum touch target size */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Offcanvas menu styling */
.offcanvas {
  max-width: 85%;
}

.offcanvas-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem;
}

.offcanvas-title {
  font-weight: 600;
  color: white;
  font-size: 1.1rem;
}

.offcanvas .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1rem;
  border-radius: 5px;
  transition: all 0.2s ease;
  min-height: 44px; /* Touch-friendly size */
  display: flex;
  align-items: center;
}

.offcanvas .nav-link:hover, 
.offcanvas .nav-link:focus,
.offcanvas .nav-link:active {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.offcanvas .nav-item {
  margin-bottom: 0.25rem;
}

/* Card styling - mobile optimized */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  margin-bottom: 1rem;
  overflow: hidden; /* Ensure content doesn't break the card */
}

.card:active {
  transform: scale(0.98); /* Subtle feedback on touch */
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.card-body {
  padding: 1rem;
}

/* Table responsiveness */
.table-responsive {
  border-radius: var(--border-radius);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.table {
  background-color: white;
  margin-bottom: 0;
}

.table thead th {
  background-color: var(--light-color);
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Allow text wrapping for transaction descriptions */
.table td {
  white-space: nowrap; /* Keep most cells as nowrap */
}

.table td.text-wrap,
.table td.description-cell {
  white-space: normal; /* Allow wrapping for description cells */
  word-break: break-word; /* Break words if needed */
  min-width: 150px; /* Minimum width for description cells */
  max-width: 300px; /* Maximum width for description cells */
}

/* Touch-friendly buttons */
.btn {
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: var(--transition);
  min-height: 44px; /* Touch-friendly size */
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), #6a5acd);
  border: none;
}

.btn-primary:hover,
.btn-primary:active {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #c82333);
  border: none;
}

/* Touch-friendly form elements */
.form-control, .form-select {
  border-radius: var(--border-radius);
  border: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
  transition: var(--transition);
  min-height: 44px; /* Touch-friendly size */
  font-size: 1rem;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(74, 107, 255, 0.25);
}

/* Larger touch targets for checkboxes and radio buttons */
.form-check-input {
  width: 1.2em;
  height: 1.2em;
  margin-top: 0.15em;
}

.form-check-label {
  padding-left: 0.25rem;
  padding-top: 0.1rem;
}

/* Alert styling */
.alert {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: var(--box-shadow);
  padding: 1rem;
}

/* Dashboard Cards */
.dashboard-card {
  text-align: center;
  padding: 1rem;
}

.dashboard-card .card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.dashboard-card .card-text {
  font-size: 1.5rem;
  font-weight: 700;
}

/* Mobile-specific styles */
@media (max-width: 576px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  h1, .h1 {
    font-size: 1.75rem;
  }
  
  h2, .h2 {
    font-size: 1.5rem;
  }
  
  /* Hide less important columns in tables */
  .table-mobile-optimized th:nth-child(n+4),
  .table-mobile-optimized td:nth-child(n+4) {
    display: none;
  }
  
  /* Full-width buttons on mobile */
  .btn-mobile-block {
    display: block;
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  /* Adjust card padding */
  .card-body {
    padding: 0.75rem;
  }
  
  /* Adjust form spacing */
  .mb-3 {
    margin-bottom: 0.75rem !important;
  }
  
  /* Improve transaction item display on mobile */
  .transaction-item .text-wrap {
    max-width: 70%;
    word-break: break-word;
    line-height: 1.4;
    margin-bottom: 0.25rem;
  }
}

/* Tablet styles */
@media (min-width: 577px) and (max-width: 991px) {
  .dashboard-card .card-text {
    font-size: 1.75rem;
  }
  
  .card:hover {
    transform: translateY(-3px);
  }
}

/* Desktop styles */
@media (min-width: 992px) {
  .navbar-brand {
    font-size: 1.5rem;
  }
  
  .navbar .btn-outline-light {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .card {
    margin-bottom: 1.5rem;
  }
  
  .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }
  
  .card-header {
    padding: 1.25rem;
  }
  
  .card-body {
    padding: 1.5rem;
  }
  
  .dashboard-card {
    padding: 1.5rem;
  }
  
  .dashboard-card .card-title {
    font-size: 1.5rem;
  }
  
  .dashboard-card .card-text {
    font-size: 2rem;
  }
}

/* PWA specific styles */
.add-to-home-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 1rem;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1030;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.add-to-home-banner.show {
  transform: translateY(0);
}

.add-to-home-banner .btn-close {
  margin-left: 1rem;
}

/* Touch gesture styles */
.transaction-item, .budget-item {
  position: relative;
  transition: transform 0.3s ease;
}

.transaction-item.show-delete, .budget-item.show-delete {
  transform: translateX(-80px);
}

.delete-btn {
  position: absolute;
  right: -80px;
  top: 0;
  height: 100%;
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--danger-color);
  color: white;
  border: none;
}

/* Pull to refresh animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
  display: inline-block;
}

/* Context menu styling */
.context-menu {
  min-width: 180px;
}

.context-menu-item {
  user-select: none;
}

/* Offline indicator */
.offline-indicator {
  position: fixed;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--warning-color);
  color: #333;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: var(--box-shadow);
  z-index: 1040;
  display: flex;
  align-items: center;
}

.offline-indicator i {
  margin-right: 0.5rem;
}

/* Accessibility improvements */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible for keyboard navigation */
a:focus-visible, button:focus-visible, input:focus-visible, select:focus-visible, textarea:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
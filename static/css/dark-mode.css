/* Dark Mode Styles */
:root {
  /* Base colors */
  --dark-bg: #121212;
  --dark-card-bg: #1e1e1e;
  --dark-header-bg: #252525;
  --dark-text: #e0e0e0;
  --dark-text-secondary: #aaaaaa;
  --dark-border: #333333;
  --dark-input-bg: #2a2a2a;
  --dark-hover: #2c2c2c;
  --dark-active: #333333;
  --dark-shadow: rgba(0, 0, 0, 0.3);
  
  /* Accent colors - coordinated with light theme */
  --dark-primary: #6a8fff;
  --dark-primary-hover: #7a9fff;
  --dark-success: #2ebd59;
  --dark-danger: #e05260;
  --dark-warning: #ffcc33;
  --dark-info: #4fc3f7;
  
  /* Transition for smooth theme switching */
  --theme-transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Apply transition to all elements for smooth theme switching */
body, body * {
  transition: var(--theme-transition);
}

body.dark-mode {
  background-color: var(--dark-bg) !important;
  color: var(--dark-text) !important;
}

/* Global fixes for white backgrounds */
body.dark-mode div,
body.dark-mode section,
body.dark-mode main,
body.dark-mode aside,
body.dark-mode header,
body.dark-mode footer {
  background-color: inherit;
}

/* Fix for Bootstrap's default backgrounds */
body.dark-mode .bg-light,
body.dark-mode .bg-white,
body.dark-mode .bg-body,
body.dark-mode .bg-body-tertiary {
  background-color: var(--dark-bg) !important;
  color: var(--dark-text) !important;
}

/* Fix for any containers with white backgrounds */
body.dark-mode .container,
body.dark-mode .container-fluid,
body.dark-mode .container-sm,
body.dark-mode .container-md,
body.dark-mode .container-lg,
body.dark-mode .container-xl,
body.dark-mode .container-xxl {
  background-color: var(--dark-bg);
}

/* Ensure all text elements have proper contrast in dark mode */
body.dark-mode h1, 
body.dark-mode h2, 
body.dark-mode h3, 
body.dark-mode h4, 
body.dark-mode h5, 
body.dark-mode h6,
body.dark-mode p,
body.dark-mode span,
body.dark-mode a:not(.btn),
body.dark-mode label,
body.dark-mode .text-muted,
body.dark-mode small {
  color: var(--dark-text);
}

/* Secondary text with slightly lower contrast but still readable */
body.dark-mode .text-muted,
body.dark-mode small,
body.dark-mode .text-secondary {
  color: var(--dark-text-secondary) !important;
}

/* Links in dark mode */
body.dark-mode a:not(.btn) {
  color: var(--dark-primary);
}

body.dark-mode a:not(.btn):hover {
  color: var(--dark-primary-hover);
}

/* Card styles */
body.dark-mode .card {
  background-color: var(--dark-card-bg) !important;
  border-color: var(--dark-border) !important;
  color: var(--dark-text) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.dark-mode .card-header {
  background-color: var(--dark-header-bg) !important;
  border-color: var(--dark-border) !important;
  color: var(--dark-text) !important;
}

body.dark-mode .card-body {
  background-color: var(--dark-card-bg) !important;
  color: var(--dark-text) !important;
}

body.dark-mode .card-footer {
  background-color: var(--dark-header-bg) !important;
  border-color: var(--dark-border) !important;
  color: var(--dark-text) !important;
}

/* Card text elements */
body.dark-mode .card-title,
body.dark-mode .card-subtitle,
body.dark-mode .card-text {
  color: var(--dark-text) !important;
}

body.dark-mode .card-subtitle {
  color: var(--dark-text-secondary) !important;
}

/* Dashboard cards */
body.dark-mode .dashboard-card {
  background-color: var(--dark-card-bg) !important;
  border-color: var(--dark-border) !important;
}

body.dark-mode .dashboard-card .card-title {
  color: var(--dark-text) !important;
}

body.dark-mode .dashboard-card .card-text {
  color: var(--dark-text) !important;
}

/* Fix for any white backgrounds in card-related elements */
body.dark-mode [class*="card"],
body.dark-mode [class*="panel"],
body.dark-mode .bg-light,
body.dark-mode .bg-white {
  background-color: var(--dark-card-bg) !important;
  color: var(--dark-text) !important;
}

/* Table styles */
body.dark-mode .table,
body.dark-mode .table-responsive {
  color: var(--dark-text);
  background-color: var(--dark-card-bg);
}

/* Force background color on all table elements */
body.dark-mode table,
body.dark-mode .table tbody,
body.dark-mode .table thead,
body.dark-mode .table tfoot,
body.dark-mode .table tr {
  background-color: var(--dark-card-bg) !important;
  color: var(--dark-text) !important;
}

body.dark-mode .table-hover tbody tr:hover {
  background-color: var(--dark-hover) !important;
  color: var(--dark-text);
}

body.dark-mode .table td, 
body.dark-mode .table th {
  border-color: var(--dark-border);
  color: var(--dark-text) !important;
  background-color: transparent !important;
}

body.dark-mode .table thead th {
  background-color: var(--dark-header-bg) !important;
  color: var(--dark-text) !important;
  border-bottom: 2px solid var(--dark-border);
  font-weight: 600;
}

body.dark-mode .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

body.dark-mode .table-striped tbody tr:nth-of-type(even) {
  background-color: var(--dark-card-bg) !important;
}

/* Table borders */
body.dark-mode .table-bordered,
body.dark-mode .table-bordered td,
body.dark-mode .table-bordered th {
  border-color: var(--dark-border) !important;
}

/* DataTables specific styling */
body.dark-mode .dataTables_wrapper {
  background-color: var(--dark-card-bg);
  color: var(--dark-text);
  padding: 0.5rem;
  border-radius: var(--border-radius);
}

body.dark-mode .dataTables_wrapper .dataTables_length,
body.dark-mode .dataTables_wrapper .dataTables_filter,
body.dark-mode .dataTables_wrapper .dataTables_info,
body.dark-mode .dataTables_wrapper .dataTables_processing,
body.dark-mode .dataTables_wrapper .dataTables_paginate {
  color: var(--dark-text) !important;
  background-color: transparent !important;
}

body.dark-mode .dataTables_wrapper .dataTables_length select,
body.dark-mode .dataTables_wrapper .dataTables_filter input {
  background-color: var(--dark-input-bg);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button {
  color: var(--dark-text) !important;
  background-color: var(--dark-card-bg) !important;
  border-color: var(--dark-border) !important;
}

body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background-color: var(--dark-hover) !important;
  color: var(--dark-text) !important;
}

body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current,
body.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background: var(--dark-primary) !important;
  color: white !important;
  border-color: var(--dark-border) !important;
}

/* Fix for any white backgrounds in table-related elements */
body.dark-mode .table-wrapper,
body.dark-mode .table-container,
body.dark-mode .table-responsive,
body.dark-mode div:has(> table) {
  background-color: var(--dark-card-bg);
}

/* Form controls */
body.dark-mode .form-control,
body.dark-mode .form-select,
body.dark-mode .form-control[readonly],
body.dark-mode .form-control:disabled,
body.dark-mode .form-select:disabled {
  background-color: var(--dark-input-bg);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

body.dark-mode .form-control::placeholder {
  color: var(--dark-text-secondary);
  opacity: 0.7;
}

body.dark-mode .form-control:focus,
body.dark-mode .form-select:focus {
  background-color: var(--dark-input-bg);
  color: var(--dark-text);
  border-color: var(--dark-primary);
  box-shadow: 0 0 0 0.25rem rgba(106, 143, 255, 0.25);
}

body.dark-mode .input-group-text {
  background-color: var(--dark-header-bg);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

/* Form labels and text */
body.dark-mode .form-label,
body.dark-mode .form-text,
body.dark-mode .form-check-label {
  color: var(--dark-text);
}

body.dark-mode .form-text {
  color: var(--dark-text-secondary);
}

/* Form check (checkboxes and radios) */
body.dark-mode .form-check-input {
  background-color: var(--dark-input-bg);
  border-color: var(--dark-border);
}

body.dark-mode .form-check-input:checked {
  background-color: var(--dark-primary);
  border-color: var(--dark-primary);
}

/* Select2 dropdown styling */
body.dark-mode .select2-container--default .select2-selection--single,
body.dark-mode .select2-container--default .select2-selection--multiple {
  background-color: var(--dark-input-bg);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

body.dark-mode .select2-container--default .select2-selection__rendered {
  color: var(--dark-text);
}

body.dark-mode .select2-dropdown {
  background-color: var(--dark-input-bg);
  border-color: var(--dark-border);
}

body.dark-mode .select2-container--default .select2-results__option {
  color: var(--dark-text);
}

body.dark-mode .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--dark-primary);
  color: white;
}

/* Navbar */
body.dark-mode .navbar {
  background: linear-gradient(135deg, #2a3a5f, #1a2a4f) !important;
  border-color: var(--dark-border);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

body.dark-mode .navbar-brand,
body.dark-mode .nav-link {
  color: var(--dark-text) !important;
}

body.dark-mode .navbar-brand {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

body.dark-mode .navbar-toggler {
  border-color: var(--dark-border);
  color: var(--dark-text);
}

body.dark-mode .navbar-toggler-icon {
  filter: brightness(2);
}

/* Offcanvas menu in dark mode */
body.dark-mode .offcanvas {
  background-color: #1a2a4f;
}

body.dark-mode .offcanvas .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Dropdown menus */
body.dark-mode .dropdown-menu {
  background-color: var(--dark-card-bg);
  border-color: var(--dark-border);
}

body.dark-mode .dropdown-item {
  color: var(--dark-text);
}

body.dark-mode .dropdown-item:hover {
  background-color: var(--dark-hover);
  color: var(--dark-text);
}

/* Buttons */
body.dark-mode .btn {
  transition: var(--theme-transition);
}

/* Primary buttons */
body.dark-mode .btn-primary {
  background: linear-gradient(135deg, var(--dark-primary), #5a7aed);
  border: none;
  color: white;
}

body.dark-mode .btn-primary:hover,
body.dark-mode .btn-primary:active {
  background: linear-gradient(135deg, var(--dark-primary-hover), #6a8afd);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Secondary buttons */
body.dark-mode .btn-outline-secondary {
  color: var(--dark-text);
  border-color: var(--dark-border);
}

body.dark-mode .btn-outline-secondary:hover {
  background-color: var(--dark-hover);
  color: var(--dark-text);
}

/* Success buttons */
body.dark-mode .btn-success {
  background-color: var(--dark-success);
  border-color: var(--dark-success);
}

body.dark-mode .btn-success:hover {
  background-color: #25a048;
  border-color: #25a048;
}

/* Danger buttons */
body.dark-mode .btn-danger {
  background-color: var(--dark-danger);
  border-color: var(--dark-danger);
}

body.dark-mode .btn-danger:hover {
  background-color: #d04050;
  border-color: #d04050;
}

/* Light buttons in dark mode */
body.dark-mode .btn-light {
  background-color: #2a2a2a;
  border-color: #333;
  color: var(--dark-text);
}

body.dark-mode .btn-light:hover {
  background-color: #333;
  color: var(--dark-text);
}

/* Modal */
body.dark-mode .modal-content {
  background-color: var(--dark-card-bg);
  border-color: var(--dark-border);
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
  border-color: var(--dark-border);
}

/* Additional fixes for common Bootstrap components */

/* Badges */
body.dark-mode .badge {
  color: white !important;
}

body.dark-mode .badge-light,
body.dark-mode .badge-secondary {
  background-color: var(--dark-header-bg) !important;
  color: var(--dark-text) !important;
}

/* Tooltips and Popovers */
body.dark-mode .tooltip-inner {
  background-color: var(--dark-header-bg) !important;
  color: var(--dark-text) !important;
  border: 1px solid var(--dark-border);
}

body.dark-mode .bs-tooltip-top .arrow::before,
body.dark-mode .bs-tooltip-auto[x-placement^="top"] .arrow::before {
  border-top-color: var(--dark-header-bg) !important;
}

body.dark-mode .bs-tooltip-right .arrow::before,
body.dark-mode .bs-tooltip-auto[x-placement^="right"] .arrow::before {
  border-right-color: var(--dark-header-bg) !important;
}

body.dark-mode .bs-tooltip-bottom .arrow::before,
body.dark-mode .bs-tooltip-auto[x-placement^="bottom"] .arrow::before {
  border-bottom-color: var(--dark-header-bg) !important;
}

body.dark-mode .bs-tooltip-left .arrow::before,
body.dark-mode .bs-tooltip-auto[x-placement^="left"] .arrow::before {
  border-left-color: var(--dark-header-bg) !important;
}

body.dark-mode .popover {
  background-color: var(--dark-card-bg) !important;
  border-color: var(--dark-border) !important;
}

body.dark-mode .popover-header {
  background-color: var(--dark-header-bg) !important;
  border-color: var(--dark-border) !important;
  color: var(--dark-text) !important;
}

body.dark-mode .popover-body {
  color: var(--dark-text) !important;
}

/* Progress bars */
body.dark-mode .progress {
  background-color: var(--dark-header-bg) !important;
}

/* Breadcrumbs */
body.dark-mode .breadcrumb {
  background-color: var(--dark-header-bg) !important;
}

body.dark-mode .breadcrumb-item {
  color: var(--dark-text-secondary) !important;
}

body.dark-mode .breadcrumb-item.active {
  color: var(--dark-text) !important;
}

body.dark-mode .breadcrumb-item + .breadcrumb-item::before {
  color: var(--dark-text-secondary) !important;
}

/* Alerts */
body.dark-mode .alert-success {
  background-color: rgba(25, 135, 84, 0.2);
  color: #75b798;
  border-color: rgba(25, 135, 84, 0.3);
}

body.dark-mode .alert-danger {
  background-color: rgba(220, 53, 69, 0.2);
  color: #ea868f;
  border-color: rgba(220, 53, 69, 0.3);
}

body.dark-mode .alert-warning {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffda6a;
  border-color: rgba(255, 193, 7, 0.3);
}

body.dark-mode .alert-info {
  background-color: rgba(13, 202, 240, 0.2);
  color: #6edff6;
  border-color: rgba(13, 202, 240, 0.3);
}

/* Pagination */
body.dark-mode .pagination {
  background-color: transparent;
}

body.dark-mode .page-link {
  background-color: var(--dark-card-bg) !important;
  border-color: var(--dark-border) !important;
  color: var(--dark-text) !important;
}

body.dark-mode .page-link:hover {
  background-color: var(--dark-hover) !important;
  border-color: var(--dark-border) !important;
  color: var(--dark-text) !important;
}

body.dark-mode .page-item.active .page-link {
  background-color: var(--dark-primary) !important;
  border-color: var(--dark-primary) !important;
  color: white !important;
}

body.dark-mode .page-item.disabled .page-link {
  background-color: var(--dark-card-bg) !important;
  color: var(--dark-text-secondary) !important;
  border-color: var(--dark-border) !important;
}

/* Bootstrap list groups */
body.dark-mode .list-group {
  background-color: var(--dark-card-bg);
  border-color: var(--dark-border);
}

body.dark-mode .list-group-item {
  background-color: var(--dark-card-bg) !important;
  border-color: var(--dark-border) !important;
  color: var(--dark-text) !important;
}

body.dark-mode .list-group-item.active {
  background-color: var(--dark-primary) !important;
  border-color: var(--dark-primary) !important;
  color: white !important;
}

body.dark-mode .list-group-item-action:hover,
body.dark-mode .list-group-item-action:focus {
  background-color: var(--dark-hover) !important;
  color: var(--dark-text) !important;
}

/* Nav tabs */
body.dark-mode .nav-tabs {
  border-color: var(--dark-border);
}

body.dark-mode .nav-tabs .nav-link {
  color: var(--dark-text);
}

body.dark-mode .nav-tabs .nav-link.active {
  background-color: var(--dark-card-bg);
  border-color: var(--dark-border) var(--dark-border) var(--dark-card-bg);
  color: var(--dark-text);
}

/* Charts */
body.dark-mode .chart-container {
  background-color: var(--dark-card-bg);
  border-radius: var(--border-radius);
  padding: 1rem;
  box-shadow: var(--box-shadow);
}

/* Chart colors for dark mode */
body.dark-mode .chart-legend,
body.dark-mode .chartjs-legend,
body.dark-mode .legend,
body.dark-mode [class*="legend"],
body.dark-mode .chart-container .legend,
body.dark-mode .chart-js-legend,
body.dark-mode ul.legend,
body.dark-mode .chart-legend li,
body.dark-mode .chartjs-legend li {
  color: var(--dark-text) !important;
  fill: var(--dark-text) !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.dark-mode .chart-title,
body.dark-mode .chartjs-title,
body.dark-mode [class*="chart"] [class*="title"],
body.dark-mode .chart-container [class*="title"] {
  color: var(--dark-text) !important;
  font-weight: 600 !important;
  margin-bottom: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Fix for legend text in all chart types */
body.dark-mode .chart-container text,
body.dark-mode svg text,
body.dark-mode [class*="chart"] text,
body.dark-mode .chartjs-render-monitor text,
body.dark-mode g.legend text,
body.dark-mode .c3-legend-item text,
body.dark-mode .highcharts-legend-item text,
body.dark-mode .apexcharts-legend-text,
body.dark-mode .nvd3 .nv-legend text {
  fill: var(--dark-text) !important;
  color: var(--dark-text) !important;
  font-weight: 500 !important;
  stroke: none !important;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;
}

/* Ensure SVG-based legends have proper contrast */
body.dark-mode .chart-container .legend rect,
body.dark-mode svg .legend rect,
body.dark-mode g.legend rect {
  stroke: var(--dark-border) !important;
}

/* Fix for any canvas-based legends */
body.dark-mode canvas {
  filter: brightness(1.05); /* Slightly increase brightness for better visibility */
}

/* Custom chart colors for better visibility in dark mode */
body.dark-mode .chart-color-1 { color: #6a8fff !important; }
body.dark-mode .chart-color-2 { color: #4fc3f7 !important; }
body.dark-mode .chart-color-3 { color: #2ebd59 !important; }
body.dark-mode .chart-color-4 { color: #ffcc33 !important; }
body.dark-mode .chart-color-5 { color: #e05260 !important; }
body.dark-mode .chart-color-6 { color: #ba68c8 !important; }
body.dark-mode .chart-color-7 { color: #f57c00 !important; }
body.dark-mode .chart-color-8 { color: #26a69a !important; }

/* HTML-based legend items */
body.dark-mode .legend-item,
body.dark-mode [class*="legend-item"],
body.dark-mode [class*="legend"] li,
body.dark-mode [class*="legend"] span,
body.dark-mode [class*="legend"] label,
body.dark-mode [class*="legend"] div,
body.dark-mode [id*="legend"] span,
body.dark-mode [id*="legend"] div {
  color: var(--dark-text) !important;
  background-color: transparent !important;
  font-weight: 500 !important;
}

/* Legend color indicators */
body.dark-mode .legend-color,
body.dark-mode .legend-indicator,
body.dark-mode .color-indicator,
body.dark-mode [class*="legend"] [class*="color"],
body.dark-mode [class*="legend"] [class*="indicator"] {
  border: 1px solid var(--dark-border) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

/* Chart tooltips */
body.dark-mode .chartjs-tooltip {
  background-color: rgba(30, 30, 30, 0.9);
  color: var(--dark-text);
  border-color: var(--dark-border);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Simple Dark Mode Toggle with Sun/Moon Icons */
.checkbox {
  opacity: 0;
  position: absolute;
}

/* Ensure proper alignment in navbar */
.navbar .me-3 {
  display: flex;
  align-items: center;
}

.checkbox-label {
  background-color: #111;
  width: 50px;
  height: 26px;
  border-radius: 50px;
  position: relative;
  padding: 5px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Icon colors */
.checkbox-label .bi-moon-fill {
  color: #f1c40f; /* Yellow for moon */
  font-size: 14px;
  margin-left: 2px;
}

.checkbox-label .bi-sun-fill {
  color: #f39c12; /* Orange for sun */
  font-size: 14px;
  margin-right: 2px;
}

/* The toggle ball */
.checkbox-label .ball {
  background-color: #fff;
  width: 22px;
  height: 22px;
  position: absolute;
  left: 2px;
  top: 2px;
  border-radius: 50%;
  transition: transform 0.2s linear;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Move the ball when checked */
.checkbox:checked + .checkbox-label .ball {
  transform: translateX(24px);
}

/* Hover effects */
.checkbox-label:hover,
.checkbox-label.toggle-hover {
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

/* Enhanced hover effects */
.checkbox-label.toggle-hover .bi-sun-fill {
  color: #ffb700;
  filter: drop-shadow(0 0 2px rgba(255, 183, 0, 0.6));
}

.checkbox-label.toggle-hover .bi-moon-fill {
  color: #ffe066;
  filter: drop-shadow(0 0 2px rgba(255, 224, 102, 0.6));
}

body.dark-mode .checkbox-label.toggle-hover .bi-moon-fill {
  color: #ffffff;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.6));
}

/* Dark mode specific styles */
body.dark-mode .checkbox-label {
  background-color: #2c3e50; /* Darker blue in dark mode */
}

body.dark-mode .checkbox-label .ball {
  background-color: #e0e0e0; /* Slightly off-white in dark mode */
}

/* Focus styles for accessibility */
.checkbox:focus + .checkbox-label {
  box-shadow: 0 0 0 2px var(--dark-primary);
}

/* Theme transition class for smoother animation */
body.theme-transition {
  transition: background-color 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55), 
              color 0.6s ease !important;
}

/* Catch-all fixes for any remaining white backgrounds */
body.dark-mode *:not([class*="btn"]):not([class*="progress-bar"]):not([class*="badge"]):not(code):not(pre):not(img):not(svg):not(path):not(input):not(textarea):not(select):not(option):not(button) {
  background-color: inherit;
}

/* Force text color for any elements that might be using default color */
body.dark-mode *:not([class*="btn"]):not([class*="progress-bar"]):not([class*="badge"]):not(code):not(pre):not(img):not(svg):not(path) {
  color: inherit;
}

/* Fix for any inline styles that might be setting background to white */
body.dark-mode [style*="background-color: white"],
body.dark-mode [style*="background-color: #fff"],
body.dark-mode [style*="background-color: #ffffff"],
body.dark-mode [style*="background: white"],
body.dark-mode [style*="background: #fff"],
body.dark-mode [style*="background: #ffffff"] {
  background-color: var(--dark-card-bg) !important;
  color: var(--dark-text) !important;
}

/* Fix for any inline styles that might be setting text color to black */
body.dark-mode [style*="color: black"],
body.dark-mode [style*="color: #000"],
body.dark-mode [style*="color: #000000"] {
  color: var(--dark-text) !important;
}
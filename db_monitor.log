2025-06-01 11:42:42,076 - INFO - Connected to database: moneytracker
2025-06-01 11:42:42,077 - INFO - Money Tracker Database Monitor
2025-06-01 11:42:42,077 - INFO - =============================
2025-06-01 11:42:42,077 - INFO - MongoDB URI: moneytrackerproj.ocpr9tj.mongodb.net/?retryWrites=true&w=majority&appName=moneytrackerproj
2025-06-01 11:42:42,077 - INFO - Database: moneytracker
2025-06-01 11:42:42,077 - INFO - Time: 2025-06-01 11:42:42.077815
2025-06-01 11:42:42,077 - INFO - -----------------------------
2025-06-01 11:42:42,077 - INFO - Analyzing collection statistics...
2025-06-01 11:42:42,787 - INFO - Collection Statistics:
2025-06-01 11:42:42,787 - INFO - =====================
2025-06-01 11:42:42,788 - INFO - 
Collection: salaries
2025-06-01 11:42:42,788 - INFO -   Document Count: 0
2025-06-01 11:42:42,788 - INFO -   Data Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:42:42,788 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Index Size: 0.01 MB
2025-06-01 11:42:42,788 - INFO -   Number of Indexes: 3
2025-06-01 11:42:42,788 - INFO - 
Collection: budgets
2025-06-01 11:42:42,788 - INFO -   Document Count: 0
2025-06-01 11:42:42,788 - INFO -   Data Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:42:42,788 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Index Size: 0.02 MB
2025-06-01 11:42:42,788 - INFO -   Number of Indexes: 4
2025-06-01 11:42:42,788 - INFO - 
Collection: users
2025-06-01 11:42:42,788 - INFO -   Document Count: 0
2025-06-01 11:42:42,788 - INFO -   Data Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:42:42,788 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Index Size: 0.02 MB
2025-06-01 11:42:42,788 - INFO -   Number of Indexes: 4
2025-06-01 11:42:42,788 - INFO - 
Collection: expenses
2025-06-01 11:42:42,788 - INFO -   Document Count: 0
2025-06-01 11:42:42,788 - INFO -   Data Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:42:42,788 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Index Size: 0.02 MB
2025-06-01 11:42:42,788 - INFO -   Number of Indexes: 5
2025-06-01 11:42:42,788 - INFO - 
Collection: categories
2025-06-01 11:42:42,788 - INFO -   Document Count: 0
2025-06-01 11:42:42,788 - INFO -   Data Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:42:42,788 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:42:42,788 - INFO -   Index Size: 0.01 MB
2025-06-01 11:42:42,788 - INFO -   Number of Indexes: 3
2025-06-01 11:42:42,788 - INFO - Analyzing index usage...
2025-06-01 11:42:42,809 - INFO - 
Indexes for collection: salaries
2025-06-01 11:42:42,833 - INFO -   Index 1: _id_
2025-06-01 11:42:42,833 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:42:42,833 - INFO -   Index 2: user_id_1_date_-1
2025-06-01 11:42:42,834 - INFO -     Key: SON([('user_id', 1), ('date', -1)])
2025-06-01 11:42:42,834 - INFO -   Index 3: is_recurring_1_next_date_1
2025-06-01 11:42:42,834 - INFO -     Key: SON([('is_recurring', 1), ('next_date', 1)])
2025-06-01 11:42:42,834 - INFO - 
Indexes for collection: budgets
2025-06-01 11:42:42,854 - INFO -   Index 1: _id_
2025-06-01 11:42:42,854 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:42:42,854 - INFO -   Index 2: user_id_1_is_active_1_period_1
2025-06-01 11:42:42,854 - INFO -     Key: SON([('user_id', 1), ('is_active', 1), ('period', 1)])
2025-06-01 11:42:42,854 - INFO -   Index 3: user_id_1_start_date_1_end_date_1
2025-06-01 11:42:42,854 - INFO -     Key: SON([('user_id', 1), ('start_date', 1), ('end_date', 1)])
2025-06-01 11:42:42,854 - INFO -   Index 4: user_id_1_category_id_1
2025-06-01 11:42:42,854 - INFO -     Key: SON([('user_id', 1), ('category_id', 1)])
2025-06-01 11:42:42,854 - INFO - 
Indexes for collection: users
2025-06-01 11:42:42,873 - INFO -   Index 1: _id_
2025-06-01 11:42:42,873 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:42:42,873 - INFO -   Index 2: username_1
2025-06-01 11:42:42,873 - INFO -     Key: SON([('username', 1)])
2025-06-01 11:42:42,873 - INFO -     Unique: True
2025-06-01 11:42:42,873 - INFO -   Index 3: email_1
2025-06-01 11:42:42,873 - INFO -     Key: SON([('email', 1)])
2025-06-01 11:42:42,873 - INFO -     Unique: True
2025-06-01 11:42:42,873 - INFO -   Index 4: password_reset_token_1
2025-06-01 11:42:42,873 - INFO -     Key: SON([('password_reset_token', 1)])
2025-06-01 11:42:42,873 - INFO - 
Indexes for collection: expenses
2025-06-01 11:42:42,892 - INFO -   Index 1: _id_
2025-06-01 11:42:42,892 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:42:42,892 - INFO -   Index 2: user_id_1_timestamp_-1
2025-06-01 11:42:42,893 - INFO -     Key: SON([('user_id', 1), ('timestamp', -1)])
2025-06-01 11:42:42,893 - INFO -   Index 3: is_recurring_1_next_date_1
2025-06-01 11:42:42,893 - INFO -     Key: SON([('is_recurring', 1), ('next_date', 1)])
2025-06-01 11:42:42,893 - INFO -   Index 4: user_id_1_category_id_1
2025-06-01 11:42:42,893 - INFO -     Key: SON([('user_id', 1), ('category_id', 1)])
2025-06-01 11:42:42,893 - INFO -   Index 5: user_id_1_date_1
2025-06-01 11:42:42,893 - INFO -     Key: SON([('user_id', 1), ('date', 1)])
2025-06-01 11:42:42,893 - INFO - 
Indexes for collection: categories
2025-06-01 11:42:42,912 - INFO -   Index 1: _id_
2025-06-01 11:42:42,912 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:42:42,912 - INFO -   Index 2: user_id_1
2025-06-01 11:42:42,912 - INFO -     Key: SON([('user_id', 1)])
2025-06-01 11:42:42,912 - INFO -   Index 3: is_global_1
2025-06-01 11:42:42,912 - INFO -     Key: SON([('is_global', 1)])
2025-06-01 11:42:42,912 - INFO - 
Index Recommendations:
2025-06-01 11:42:42,981 - INFO - Analyzing data distribution...
2025-06-01 11:42:43,088 - INFO - Analyzing slow queries (>100ms)...
2025-06-01 11:43:21,511 - INFO - Connected to database: moneytracker
2025-06-01 11:43:21,511 - INFO - Money Tracker Database Monitor
2025-06-01 11:43:21,511 - INFO - =============================
2025-06-01 11:43:21,511 - INFO - MongoDB URI: moneytrackerproj.ocpr9tj.mongodb.net/?retryWrites=true&w=majority&appName=moneytrackerproj
2025-06-01 11:43:21,512 - INFO - Database: moneytracker
2025-06-01 11:43:21,512 - INFO - Time: 2025-06-01 11:43:21.512031
2025-06-01 11:43:21,512 - INFO - -----------------------------
2025-06-01 11:43:21,512 - INFO - Analyzing collection statistics...
2025-06-01 11:43:22,328 - INFO - Collection Statistics:
2025-06-01 11:43:22,328 - INFO - =====================
2025-06-01 11:43:22,328 - INFO - 
Collection: salaries
2025-06-01 11:43:22,328 - INFO -   Document Count: 0
2025-06-01 11:43:22,328 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,328 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,328 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,328 - INFO -   Index Size: 0.01 MB
2025-06-01 11:43:22,328 - INFO -   Number of Indexes: 3
2025-06-01 11:43:22,328 - INFO - 
Collection: budgets
2025-06-01 11:43:22,328 - INFO -   Document Count: 0
2025-06-01 11:43:22,328 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,328 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,328 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,329 - INFO -   Index Size: 0.02 MB
2025-06-01 11:43:22,329 - INFO -   Number of Indexes: 4
2025-06-01 11:43:22,329 - INFO - 
Collection: users
2025-06-01 11:43:22,329 - INFO -   Document Count: 0
2025-06-01 11:43:22,329 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,329 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,329 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,329 - INFO -   Index Size: 0.02 MB
2025-06-01 11:43:22,329 - INFO -   Number of Indexes: 4
2025-06-01 11:43:22,329 - INFO - 
Collection: expenses
2025-06-01 11:43:22,329 - INFO -   Document Count: 0
2025-06-01 11:43:22,329 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,329 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,329 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,329 - INFO -   Index Size: 0.02 MB
2025-06-01 11:43:22,329 - INFO -   Number of Indexes: 5
2025-06-01 11:43:22,329 - INFO - 
Collection: categories
2025-06-01 11:43:22,329 - INFO -   Document Count: 0
2025-06-01 11:43:22,329 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,329 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,329 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,329 - INFO -   Index Size: 0.01 MB
2025-06-01 11:43:22,329 - INFO -   Number of Indexes: 3
2025-06-01 11:43:22,330 - INFO - Analyzing index usage...
2025-06-01 11:43:22,350 - INFO - 
Indexes for collection: salaries
2025-06-01 11:43:22,376 - INFO -   Index 1: _id_
2025-06-01 11:43:22,376 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:43:22,376 - INFO -   Index 2: user_id_1_date_-1
2025-06-01 11:43:22,376 - INFO -     Key: SON([('user_id', 1), ('date', -1)])
2025-06-01 11:43:22,376 - INFO -   Index 3: is_recurring_1_next_date_1
2025-06-01 11:43:22,376 - INFO -     Key: SON([('is_recurring', 1), ('next_date', 1)])
2025-06-01 11:43:22,376 - INFO - 
Indexes for collection: budgets
2025-06-01 11:43:22,402 - INFO -   Index 1: _id_
2025-06-01 11:43:22,402 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:43:22,402 - INFO -   Index 2: user_id_1_is_active_1_period_1
2025-06-01 11:43:22,402 - INFO -     Key: SON([('user_id', 1), ('is_active', 1), ('period', 1)])
2025-06-01 11:43:22,403 - INFO -   Index 3: user_id_1_start_date_1_end_date_1
2025-06-01 11:43:22,403 - INFO -     Key: SON([('user_id', 1), ('start_date', 1), ('end_date', 1)])
2025-06-01 11:43:22,403 - INFO -   Index 4: user_id_1_category_id_1
2025-06-01 11:43:22,403 - INFO -     Key: SON([('user_id', 1), ('category_id', 1)])
2025-06-01 11:43:22,403 - INFO - 
Indexes for collection: users
2025-06-01 11:43:22,427 - INFO -   Index 1: _id_
2025-06-01 11:43:22,427 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:43:22,427 - INFO -   Index 2: username_1
2025-06-01 11:43:22,427 - INFO -     Key: SON([('username', 1)])
2025-06-01 11:43:22,428 - INFO -     Unique: True
2025-06-01 11:43:22,428 - INFO -   Index 3: email_1
2025-06-01 11:43:22,428 - INFO -     Key: SON([('email', 1)])
2025-06-01 11:43:22,428 - INFO -     Unique: True
2025-06-01 11:43:22,428 - INFO -   Index 4: password_reset_token_1
2025-06-01 11:43:22,428 - INFO -     Key: SON([('password_reset_token', 1)])
2025-06-01 11:43:22,428 - INFO - 
Indexes for collection: expenses
2025-06-01 11:43:22,453 - INFO -   Index 1: _id_
2025-06-01 11:43:22,453 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:43:22,453 - INFO -   Index 2: user_id_1_timestamp_-1
2025-06-01 11:43:22,453 - INFO -     Key: SON([('user_id', 1), ('timestamp', -1)])
2025-06-01 11:43:22,453 - INFO -   Index 3: is_recurring_1_next_date_1
2025-06-01 11:43:22,453 - INFO -     Key: SON([('is_recurring', 1), ('next_date', 1)])
2025-06-01 11:43:22,453 - INFO -   Index 4: user_id_1_category_id_1
2025-06-01 11:43:22,453 - INFO -     Key: SON([('user_id', 1), ('category_id', 1)])
2025-06-01 11:43:22,453 - INFO -   Index 5: user_id_1_date_1
2025-06-01 11:43:22,453 - INFO -     Key: SON([('user_id', 1), ('date', 1)])
2025-06-01 11:43:22,453 - INFO - 
Indexes for collection: categories
2025-06-01 11:43:22,478 - INFO -   Index 1: _id_
2025-06-01 11:43:22,478 - INFO -     Key: SON([('_id', 1)])
2025-06-01 11:43:22,478 - INFO -   Index 2: user_id_1
2025-06-01 11:43:22,479 - INFO -     Key: SON([('user_id', 1)])
2025-06-01 11:43:22,479 - INFO -   Index 3: is_global_1
2025-06-01 11:43:22,479 - INFO -     Key: SON([('is_global', 1)])
2025-06-01 11:43:22,479 - INFO - 
Index Recommendations:
2025-06-01 11:43:22,555 - INFO - Analyzing data distribution...
2025-06-01 11:43:22,680 - INFO - Analyzing slow queries (>100ms)...
2025-06-01 11:43:22,704 - WARNING - Profiling not available: CMD_NOT_ALLOWED: profile, full error: {'ok': 0, 'errmsg': 'CMD_NOT_ALLOWED: profile', 'code': 8000, 'codeName': 'AtlasError'}
2025-06-01 11:43:22,704 - INFO - Note: MongoDB Atlas does not support the profile command.
2025-06-01 11:43:22,704 - INFO - To analyze slow queries, you can:
2025-06-01 11:43:22,704 - INFO - 1. Use MongoDB Atlas Performance Advisor in the Atlas dashboard
2025-06-01 11:43:22,704 - INFO - 2. Enable query logging in your application code
2025-06-01 11:43:22,704 - INFO - 3. Use MongoDB Atlas Data Explorer to analyze query patterns
2025-06-01 11:43:22,705 - INFO - 
Database Optimization Recommendations:
2025-06-01 11:43:22,705 - INFO - ====================================
2025-06-01 11:43:22,705 - INFO - Analyzing collection statistics...
2025-06-01 11:43:22,913 - INFO - Collection Statistics:
2025-06-01 11:43:22,913 - INFO - =====================
2025-06-01 11:43:22,913 - INFO - 
Collection: salaries
2025-06-01 11:43:22,913 - INFO -   Document Count: 0
2025-06-01 11:43:22,914 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,914 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,914 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,914 - INFO -   Index Size: 0.01 MB
2025-06-01 11:43:22,914 - INFO -   Number of Indexes: 3
2025-06-01 11:43:22,914 - INFO - 
Collection: budgets
2025-06-01 11:43:22,914 - INFO -   Document Count: 0
2025-06-01 11:43:22,914 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,914 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,914 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,914 - INFO -   Index Size: 0.02 MB
2025-06-01 11:43:22,914 - INFO -   Number of Indexes: 4
2025-06-01 11:43:22,914 - INFO - 
Collection: users
2025-06-01 11:43:22,914 - INFO -   Document Count: 0
2025-06-01 11:43:22,914 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,914 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,914 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,914 - INFO -   Index Size: 0.02 MB
2025-06-01 11:43:22,914 - INFO -   Number of Indexes: 4
2025-06-01 11:43:22,914 - INFO - 
Collection: expenses
2025-06-01 11:43:22,914 - INFO -   Document Count: 0
2025-06-01 11:43:22,914 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,914 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,914 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,915 - INFO -   Index Size: 0.02 MB
2025-06-01 11:43:22,915 - INFO -   Number of Indexes: 5
2025-06-01 11:43:22,915 - INFO - 
Collection: categories
2025-06-01 11:43:22,915 - INFO -   Document Count: 0
2025-06-01 11:43:22,915 - INFO -   Data Size: 0.00 MB
2025-06-01 11:43:22,915 - INFO -   Avg Object Size: 0.00 KB
2025-06-01 11:43:22,915 - INFO -   Storage Size: 0.00 MB
2025-06-01 11:43:22,915 - INFO -   Index Size: 0.01 MB
2025-06-01 11:43:22,915 - INFO -   Number of Indexes: 3
2025-06-01 11:43:22,915 - INFO - 
3. General Recommendations:
2025-06-01 11:43:22,915 - INFO -   - Run db_optimization.py to create optimal indexes
2025-06-01 11:43:22,915 - INFO -   - Implement query caching for expensive operations
2025-06-01 11:43:22,915 - INFO -   - Use aggregation pipelines for complex reports
2025-06-01 11:43:22,915 - INFO -   - Consider implementing data archiving for historical data
2025-06-01 11:43:22,915 - INFO -   - Monitor slow queries regularly
2025-06-01 11:43:22,915 - INFO - 
Database analysis complete.

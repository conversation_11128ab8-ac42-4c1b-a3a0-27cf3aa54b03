{% extends "base.html" %}

{% block content %}
<div class="row">
  <div class="col-12 mb-4">
    <div class="card">
      <div class="card-header">
        <h3 class="mb-3">
          <i class="bi bi-list-ul me-2"></i>All Transactions
        </h3>
        
        <!-- Mobile-optimized filter form -->
        <form method="GET" action="{{ url_for('view_transactions') }}" class="mb-3">
          <div class="row g-2">
            <div class="col-12 col-md-auto flex-md-grow-1">
              <div class="input-group">
                <span class="input-group-text d-none d-md-flex">From</span>
                <input type="date" class="form-control" name="start_date" value="{{ start_date }}" aria-label="Start date">
                <span class="input-group-text">to</span>
                <input type="date" class="form-control" name="end_date" value="{{ end_date }}" aria-label="End date">
              </div>
            </div>
            <div class="col-12 col-md-auto">
              <button type="submit" class="btn btn-primary w-100">
                <i class="bi bi-filter me-1"></i>Filter
              </button>
            </div>
            <!-- Hidden input to reset to page 1 when filtering -->
            <input type="hidden" name="page" value="1">
          </div>
        </form>
        
        <!-- Action buttons - full width on mobile -->
        <div class="d-flex flex-column flex-md-row gap-2">
          <a href="{{ url_for('add_expense') }}" class="btn btn-primary btn-mobile-block">
            <i class="bi bi-plus-circle me-2"></i>Add Transaction
          </a>
          <a href="{{ url_for('view_transactions', download='csv', start_date=start_date, end_date=end_date) }}" class="btn btn-primary btn-mobile-block">
            <i class="bi bi-download me-2"></i>Download CSV
          </a>
        </div>
      </div>
      
      <div class="card-body p-0">
        <!-- Mobile card view for transactions (visible on small screens) -->
        <div class="d-md-none">
          {% for expense in expenses %}
          <div class="transaction-item p-3 border-bottom" data-id="{{ expense.id }}">
            <div class="d-flex justify-content-between align-items-start mb-2">
              <div>
                <div class="fw-bold">{{ expense.date.strftime('%b %d, %Y') }}</div>
                <div class="text-muted small">{{ expense.category_name }}</div>
              </div>
              <div class="{% if expense.transaction_type == 'CR' %}text-success{% else %}text-danger{% endif %} fw-bold">
                {% if expense.transaction_type == 'CR' %}+{% else %}-{% endif %}₹{{ "%.2f"|format(expense.amount) }}
              </div>
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <div class="text-wrap me-2">{{ expense.description }}</div>
              <div>
                {% if expense.transaction_type == 'CR' %}
                  <span class="badge bg-success">Credit</span>
                {% else %}
                  <span class="badge bg-danger">Debit</span>
                {% endif %}
              </div>
            </div>
            <div class="mt-2 d-flex justify-content-between align-items-center">
              <div class="text-muted small">
                <i class="bi bi-clock me-1"></i>{{ expense.timestamp.strftime('%b %d, %H:%M') }}
              </div>
              <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary edit-transaction-btn" data-id="{{ expense.id }}">
                  <i class="bi bi-pencil"></i>
                </button>
                <a href="{{ url_for('delete_expense', id=expense.id) }}" class="btn btn-sm btn-outline-danger"
                  onclick="return confirm('Are you sure you want to delete this transaction?')">
                  <i class="bi bi-trash"></i>
                </a>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
        
        <!-- Traditional table view (visible on medium and larger screens) -->
        <div class="table-responsive d-none d-md-block">
          <table class="table table-hover mb-0">
            <thead>
              <tr>
                <th>Date</th>
                <th>Type</th>
                <th>Category</th>
                <th>Amount</th>
                <th>Description</th>
                <th class="d-none d-lg-table-cell">Added On</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for expense in expenses %}
              <tr class="transaction-item" data-id="{{ expense.id }}">
                <td>{{ expense.date.strftime('%Y-%m-%d') }}</td>
                <td>
                  {% if expense.transaction_type == 'CR' %}
                    <span class="badge bg-success">Credit</span>
                  {% else %}
                    <span class="badge bg-danger">Debit</span>
                  {% endif %}
                </td>
                <td>
                  <form method="POST" action="{{ url_for('update_transaction_category', id=expense.id) }}" class="d-inline">
                    <select name="category" class="form-select form-select-sm" onchange="this.form.submit()">
                      {% for category in categories %}
                        <option value="{{ category.id }}" {% if category.name == expense.category_name %}selected{% endif %}>
                          {{ category.name }}
                        </option>
                      {% endfor %}
                    </select>
                  </form>
                </td>
                <td class="{% if expense.transaction_type == 'CR' %}text-success{% else %}text-danger{% endif %}">
                  {% if expense.transaction_type == 'CR' %}+{% else %}-{% endif %}₹{{ "%.2f"|format(expense.amount) }}
                </td>
                <td class="description-cell">{{ expense.description }}</td>
                <td class="d-none d-lg-table-cell">{{ expense.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                <td>
                  <div class="d-flex gap-1">
                    <button class="btn btn-sm btn-outline-primary edit-transaction-btn" data-id="{{ expense.id }}">
                      <i class="bi bi-pencil"></i>
                    </button>
                    <a href="{{ url_for('delete_expense', id=expense.id) }}" class="btn btn-sm btn-outline-danger"
                      onclick="return confirm('Are you sure you want to delete this transaction?')">
                      <i class="bi bi-trash"></i>
                    </a>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        
        <!-- Empty state -->
        {% if not expenses %}
        <div class="text-center py-5">
          <div class="mb-3">
            <i class="bi bi-receipt text-muted" style="font-size: 3rem;"></i>
          </div>
          <h5>No transactions found</h5>
          <p class="text-muted">Try adjusting your filters or add a new transaction</p>
          <a href="{{ url_for('add_expense') }}" class="btn btn-primary mt-2">
            <i class="bi bi-plus-circle me-2"></i>Add Transaction
          </a>
        </div>
        {% endif %}
      </div>
      
      <!-- Pagination controls -->
      {% if pagination and pagination.total_pages > 1 %}
      <div class="card-footer">
        <nav aria-label="Transaction pagination">
          <ul class="pagination justify-content-center mb-0">
            <!-- Previous page button -->
            <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
              <a class="page-link" href="{{ url_for('view_transactions', page=pagination.page-1, start_date=start_date, end_date=end_date) }}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
            
            <!-- Page numbers -->
            {% set start_page = [1, pagination.page - 2]|max %}
            {% set end_page = [pagination.total_pages, pagination.page + 2]|min %}
            
            {% if start_page > 1 %}
              <li class="page-item">
                <a class="page-link" href="{{ url_for('view_transactions', page=1, start_date=start_date, end_date=end_date) }}">1</a>
              </li>
              {% if start_page > 2 %}
                <li class="page-item disabled">
                  <span class="page-link">...</span>
                </li>
              {% endif %}
            {% endif %}
            
            {% for p in range(start_page, end_page + 1) %}
              <li class="page-item {% if p == pagination.page %}active{% endif %}">
                <a class="page-link" href="{{ url_for('view_transactions', page=p, start_date=start_date, end_date=end_date) }}">{{ p }}</a>
              </li>
            {% endfor %}
            
            {% if end_page < pagination.total_pages %}
              {% if end_page < pagination.total_pages - 1 %}
                <li class="page-item disabled">
                  <span class="page-link">...</span>
                </li>
              {% endif %}
              <li class="page-item">
                <a class="page-link" href="{{ url_for('view_transactions', page=pagination.total_pages, start_date=start_date, end_date=end_date) }}">{{ pagination.total_pages }}</a>
              </li>
            {% endif %}
            
            <!-- Next page button -->
            <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
              <a class="page-link" href="{{ url_for('view_transactions', page=pagination.page+1, start_date=start_date, end_date=end_date) }}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
          </ul>
        </nav>
        
        <!-- Pagination info -->
        <div class="text-center text-muted mt-2 small">
          Showing {{ (pagination.page - 1) * pagination.per_page + 1 }}-{{ [pagination.page * pagination.per_page, pagination.total]|min }} of {{ pagination.total }} transactions
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>

<!-- Floating action button for mobile -->
<div class="d-md-none position-fixed bottom-0 end-0 m-4" style="z-index: 1000;">
  <a href="{{ url_for('add_expense') }}" class="btn btn-primary rounded-circle shadow" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
    <i class="bi bi-plus" style="font-size: 1.5rem;"></i>
  </a>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Handle edit transaction button clicks
  document.querySelectorAll('.edit-transaction-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const transactionId = this.dataset.id;
      window.location.href = `/edit_transaction/${transactionId}`;
    });
  });
  
  // Add swipe-to-delete functionality for mobile
  document.addEventListener('DOMContentLoaded', function() {
    // This is handled by the touch-gestures.js file
  });
</script>
{% endblock %}
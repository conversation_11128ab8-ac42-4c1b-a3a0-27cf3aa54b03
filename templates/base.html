<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="description" content="Track your expenses and income with Money Tracker, a simple and intuitive financial management app">
  <meta name="theme-color" content="#4a6bff">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <title>Money Tracker</title>
  
  <!-- PWA manifest -->
  <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
  
  <!-- Favicon and app icons -->
  <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/icons/icon-72x72.png') }}">
  <link rel="apple-touch-icon" href="{{ url_for('static', filename='images/icons/icon-192x192.png') }}">
  
  <!-- Apple splash screens -->
  <link rel="apple-touch-startup-image" href="{{ url_for('static', filename='images/icons/icon-512x512.png') }}">
  
  <!-- Stylesheets -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-mode.css') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-ui.css') }}">
  
  <!-- Additional libraries for enhanced features -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/nouislider@15.5.1/dist/nouislider.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" as="script">
  <link rel="preload" href="{{ url_for('static', filename='js/app.js') }}" as="script">
</head>

<body>
  <!-- Sticky navbar with improved mobile layout -->
  <nav class="navbar navbar-dark">
    <div class="container">
      <!-- Brand -->
      <a class="navbar-brand" href="{{ url_for('index') if current_user.is_authenticated else url_for('home') }}">
        <i class="bi bi-wallet2 me-2"></i>Money Tracker
      </a>
      
      <div class="d-flex align-items-center">
        <!-- Simple Dark Mode Toggle with Sun/Moon Icons -->
        <div class="me-3">
          <input type="checkbox" class="checkbox" id="darkModeToggle">
          <label for="darkModeToggle" class="checkbox-label" title="Toggle Dark Mode">
            <i class="bi bi-moon-fill"></i>
            <i class="bi bi-sun-fill"></i>
            <span class="ball"></span>
          </label>
        </div>
        
        {% if current_user.is_authenticated %}
        <!-- Always visible profile and logout buttons -->
        <div class="d-none d-sm-flex me-2">
          <a href="{{ url_for('profile') }}" class="btn btn-outline-light btn-sm me-2" aria-label="Profile">
            <i class="bi bi-person-circle me-1"></i><span class="d-none d-md-inline">Profile</span>
          </a>
          <a href="{{ url_for('logout') }}" class="btn btn-outline-light btn-sm logout-link" aria-label="Logout">
            <i class="bi bi-box-arrow-right me-1"></i><span class="d-none d-md-inline">Logout</span>
          </a>
        </div>
        {% else %}
        <!-- Always visible login and register buttons for non-authenticated users -->
        <div class="d-none d-sm-flex me-2">
          <a href="{{ url_for('login') }}" class="btn btn-outline-light btn-sm me-2" aria-label="Login">
            <i class="bi bi-box-arrow-in-right me-1"></i><span class="d-none d-md-inline">Login</span>
          </a>
          <a href="{{ url_for('register') }}" class="btn btn-outline-light btn-sm" aria-label="Register">
            <i class="bi bi-person-plus me-1"></i><span class="d-none d-md-inline">Register</span>
          </a>
        </div>
        {% endif %}
        
        <!-- Hamburger button - larger touch target -->
        <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasNavbar" aria-label="Menu">
          <span class="navbar-toggler-icon"></span>
        </button>
      </div>
      
      <!-- Offcanvas sidebar for menu items -->
      <div class="offcanvas offcanvas-end bg-dark text-white" tabindex="-1" id="offcanvasNavbar" aria-labelledby="offcanvasNavbarLabel">
        <div class="offcanvas-header">
          <h5 class="offcanvas-title" id="offcanvasNavbarLabel">
            {% if current_user.is_authenticated %}
            <i class="bi bi-person-check me-1"></i>Welcome, {{ current_user.username }}
            {% else %}
            <i class="bi bi-wallet2 me-1"></i>Money Tracker
            {% endif %}
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
          <ul class="navbar-nav">
            {% if current_user.is_authenticated %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('index') }}">
                <i class="bi bi-speedometer2 me-2"></i>Dashboard
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('view_transactions') }}">
                <i class="bi bi-list-ul me-2"></i>All Transactions
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('add_expense') }}">
                <i class="bi bi-cash-stack me-2"></i>Add Expense
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('add_salary') }}">
                <i class="bi bi-bank me-2"></i>Add Salary
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('salary_visualization') }}">
                <i class="bi bi-graph-up me-2"></i>Salary Overview
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('manage_categories') }}">
                <i class="bi bi-tags me-2"></i>Categories
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('manage_budgets') }}">
                <i class="bi bi-wallet2 me-2"></i>Budgets
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('budget_overview') }}">
                <i class="bi bi-pie-chart me-2"></i>Budget Overview
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('recurring_transactions') }}">
                <i class="bi bi-arrow-repeat me-2"></i>Recurring Transactions
              </a>
            </li>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-file-earmark-bar-graph me-2"></i>Reports & Analytics
              </a>
              <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                <li>
                  <a class="dropdown-item" href="{{ url_for('reports') }}">
                    <i class="bi bi-graph-up me-2"></i>Reports Overview
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="{{ url_for('enhanced_features.reports_dashboard') }}">
                    <i class="bi bi-bar-chart-fill me-2"></i>Enhanced Dashboard
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="{{ url_for('enhanced_features.report_cards') }}">
                    <i class="bi bi-card-list me-2"></i>Report Cards
                  </a>
                </li>
              </ul>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('security_settings') }}">
                <i class="bi bi-shield-lock me-2"></i>Security Settings
              </a>
            </li>
            
            <!-- Help & Support -->
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="helpDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-question-circle me-2"></i>Help & Support
              </a>
              <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="helpDropdown">
                <li>
                  <a class="dropdown-item" href="#" id="start-tour-menu">
                    <i class="bi bi-info-circle me-2"></i>App Tour
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="#" onclick="showKeyboardShortcutsModal()">
                    <i class="bi bi-keyboard me-2"></i>Keyboard Shortcuts
                  </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#feedbackModal">
                    <i class="bi bi-chat-dots me-2"></i>Send Feedback
                  </a>
                </li>
              </ul>
            </li>
            
            <!-- Mobile-only profile and logout options -->
            <li class="nav-item d-sm-none mt-3">
              <a class="nav-link" href="{{ url_for('profile') }}">
                <i class="bi bi-person-circle me-2"></i>Profile
              </a>
            </li>
            <li class="nav-item d-sm-none">
              <a class="nav-link logout-link" href="{{ url_for('logout') }}">
                <i class="bi bi-box-arrow-right me-2"></i>Logout
              </a>
            </li>
            {% else %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('home') }}">
                <i class="bi bi-house me-2"></i>Home
              </a>
            </li>
            
            <!-- Mobile-only login and register options -->
            <li class="nav-item d-sm-none mt-3">
              <a class="nav-link" href="{{ url_for('login') }}">
                <i class="bi bi-box-arrow-in-right me-2"></i>Login
              </a>
            </li>
            <li class="nav-item d-sm-none">
              <a class="nav-link" href="{{ url_for('register') }}">
                <i class="bi bi-person-plus me-2"></i>Register
              </a>
            </li>
            {% endif %}
          </ul>
          
          <!-- Add to Home Screen button for PWA -->
          <div class="mt-4">
            <button id="add-to-home" class="btn btn-outline-light w-100" style="display: none;">
              <i class="bi bi-download me-2"></i>Add to Home Screen
            </button>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main content container -->
  <div class="container mt-4">
    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    {% for category, message in messages %}
    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endfor %}
    {% endif %}
    {% endwith %}

    <!-- Page content -->
    {% block content %}{% endblock %}
  </div>
  
  <!-- Offline indicator (hidden by default) -->
  <div id="offline-indicator" class="offline-indicator" style="display: none;">
    <i class="bi bi-wifi-off"></i>
    <span>You are offline</span>
  </div>
  
  <!-- Add to Home banner (hidden by default) -->
  <div id="add-to-home-banner" class="add-to-home-banner">
    <div>
      <strong>Add Money Tracker to your home screen</strong>
      <p class="mb-0 text-muted">For a better experience and offline access</p>
    </div>
    <div class="d-flex align-items-center">
      <button id="add-to-home-banner-btn" class="btn btn-primary me-2">Add</button>
      <button type="button" class="btn-close" id="close-add-to-home-banner"></button>
    </div>
  </div>
  
  <!-- Feedback Modal -->
  <div class="modal fade" id="feedbackModal" tabindex="-1" aria-labelledby="feedbackModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="feedbackModalLabel">
            <i class="bi bi-chat-dots me-2"></i>Send Feedback
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="feedbackForm">
            <div class="mb-3">
              <label for="feedbackType" class="form-label">Feedback Type</label>
              <select class="form-select" id="feedbackType" required>
                <option value="">Select type</option>
                <option value="bug">Bug Report</option>
                <option value="feature">Feature Request</option>
                <option value="suggestion">Suggestion</option>
                <option value="other">Other</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="feedbackSubject" class="form-label">Subject</label>
              <input type="text" class="form-control" id="feedbackSubject" placeholder="Brief description" required>
            </div>
            <div class="mb-3">
              <label for="feedbackMessage" class="form-label">Message</label>
              <textarea class="form-control" id="feedbackMessage" rows="5" placeholder="Please provide details..." required></textarea>
            </div>
            <div class="form-check mb-3">
              <input class="form-check-input" type="checkbox" id="includeScreenshot">
              <label class="form-check-label" for="includeScreenshot">
                Include screenshot of current page
              </label>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="submitFeedback">
            <i class="bi bi-send me-1"></i>Send Feedback
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/moment@2.29.1/moment.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/nouislider@15.5.1/dist/nouislider.min.js"></script>
  <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
  
  <!-- Custom scripts -->
  <script src="{{ url_for('static', filename='js/app.js') }}"></script>
  <script src="{{ url_for('static', filename='js/touch-gestures.js') }}"></script>
  <script src="{{ url_for('static', filename='js/dark-mode.js') }}"></script>
  <script src="{{ url_for('static', filename='js/enhanced-charts.js') }}"></script>
  <script src="{{ url_for('static', filename='js/advanced-filters.js') }}"></script>
  
  <!-- Enhanced UX Features -->
  <script src="{{ url_for('static', filename='js/utility.js') }}"></script>
  <script src="{{ url_for('static', filename='js/guided-tour.js') }}"></script>
  <!-- Load keyboard shortcuts with defer to ensure DOM is fully loaded -->
  <script src="{{ url_for('static', filename='js/keyboard-shortcuts.js') }}" defer></script>
  <script src="{{ url_for('static', filename='js/feedback.js') }}"></script>
  
  <!-- Offline detection -->
  <script>
    // Show/hide offline indicator
    function updateOnlineStatus() {
      const indicator = document.getElementById('offline-indicator');
      if (navigator.onLine) {
        indicator.style.display = 'none';
      } else {
        indicator.style.display = 'flex';
      }
    }
    
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    updateOnlineStatus(); // Initial check
    
    // Close Add to Home banner
    document.getElementById('close-add-to-home-banner').addEventListener('click', function() {
      document.getElementById('add-to-home-banner').style.display = 'none';
      localStorage.setItem('pwa-banner-dismissed', 'true');
    });
    
    // Only show banner if not already dismissed and not on iOS (which has its own mechanism)
    if (!localStorage.getItem('pwa-banner-dismissed') && !navigator.standalone && !(/iPad|iPhone|iPod/.test(navigator.userAgent))) {
      // Show banner after 5 seconds
      setTimeout(() => {
        document.getElementById('add-to-home-banner').style.display = 'flex';
      }, 5000);
    }
  </script>
  
  <!-- Additional page-specific scripts -->
  {% block scripts %}{% endblock %}
</body>

</html>
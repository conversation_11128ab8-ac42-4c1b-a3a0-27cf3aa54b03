{% extends "base.html" %}

{% block content %}
<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <h3 class="mb-0">
            <i class="bi bi-pencil-square me-2"></i>Edit Recurring Income
          </h3>
        </div>
        <div class="card-body">
          <form method="POST" class="needs-validation" novalidate>
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="amount" class="form-label">Amount (₹)</label>
                <div class="input-group">
                  <span class="input-group-text">₹</span>
                  <input type="number" step="0.01" class="form-control" id="amount" name="amount" value="{{ salary.amount }}" required>
                </div>
                <div class="invalid-feedback">
                  Please enter a valid amount.
                </div>
              </div>
              <div class="col-md-6">
                <label for="description" class="form-label">Description</label>
                <input type="text" class="form-control" id="description" name="description" value="{{ salary.description }}">
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="recurrence_type" class="form-label">Recurrence Pattern</label>
                <select class="form-select" id="recurrence_type" name="recurrence_type" required>
                  <option value="weekly" {% if salary.recurrence_type == 'weekly' %}selected{% endif %}>Weekly</option>
                  <option value="bi-weekly" {% if salary.recurrence_type == 'bi-weekly' %}selected{% endif %}>Bi-weekly</option>
                  <option value="monthly" {% if salary.recurrence_type == 'monthly' %}selected{% endif %}>Monthly</option>
                </select>
              </div>
              
              <div class="col-md-6" id="monthly_options" {% if salary.recurrence_type != 'monthly' %}style="display: none;"{% endif %}>
                <label for="recurrence_day" class="form-label">Day of Month</label>
                <select class="form-select" id="recurrence_day" name="recurrence_day">
                  {% for day in range(1, 32) %}
                  <option value="{{ day }}" {% if salary.recurrence_day == day %}selected{% endif %}>{{ day }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="recurrence_end_date" class="form-label">End Date (Optional)</label>
              <input type="date" class="form-control" id="recurrence_end_date" name="recurrence_end_date" value="{{ end_date }}">
              <div class="form-text">Leave blank for no end date</div>
            </div>
            
            <div class="mb-3">
              <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Next occurrence:</strong> 
                {% if salary.next_date %}
                {{ salary.next_date.strftime('%Y-%m-%d') }}
                {% else %}
                Not scheduled
                {% endif %}
              </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
              <a href="{{ url_for('recurring_transactions') }}" class="btn btn-outline-secondary me-md-2">Cancel</a>
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-save me-1"></i>Update Recurring Income
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Form validation
  (function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms)
      .forEach(function (form) {
        form.addEventListener('submit', function (event) {
          if (!form.checkValidity()) {
            event.preventDefault()
            event.stopPropagation()
          }
          form.classList.add('was-validated')
        }, false)
      })
  })()
  
  // Show/hide monthly options based on recurrence type
  const recurrenceTypeSelect = document.getElementById('recurrence_type');
  const monthlyOptions = document.getElementById('monthly_options');
  
  recurrenceTypeSelect.addEventListener('change', function() {
    monthlyOptions.style.display = this.value === 'monthly' ? 'block' : 'none';
  });
</script>
{% endblock %}
{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
  <div class="col-md-6 col-lg-4">
    <div class="card">
      <div class="card-header text-center">
        <h3 class="mb-0">
          <i class="bi bi-person-plus me-2"></i>Register
        </h3>
      </div>
      <div class="card-body">
        <form method="POST" class="needs-validation" novalidate>
          <div class="mb-3">
            <label for="username" class="form-label">Username</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="bi bi-person"></i>
              </span>
              <input type="text" class="form-control" id="username" name="username" required>
            </div>
            <div class="invalid-feedback">
              Please choose a username.
            </div>
          </div>
          <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="bi bi-envelope"></i>
              </span>
              <input type="email" class="form-control" id="email" name="email" required>
            </div>
            <div class="invalid-feedback">
              Please enter a valid email address.
            </div>
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">Password</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="bi bi-lock"></i>
              </span>
              <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <div class="invalid-feedback">
              Please enter a password.
            </div>
          </div>
          <div class="mb-3">
            <label for="confirm_password" class="form-label">Confirm Password</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="bi bi-lock"></i>
              </span>
              <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
            </div>
            <div class="invalid-feedback">
              Please confirm your password.
            </div>
          </div>
          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-person-plus me-1"></i>Register
            </button>
          </div>
        </form>
        <div class="text-center mt-3">
          <p class="mb-0">Already have an account? <a href="{{ url_for('login') }}">Login</a></p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Form validation
  (function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms)
      .forEach(function (form) {
        form.addEventListener('submit', function (event) {
          if (!form.checkValidity()) {
            event.preventDefault()
            event.stopPropagation()
          }
          form.classList.add('was-validated')
        }, false)
      })
  })()
</script>
{% endblock %}
{% extends "base.html" %}

{% block content %}
<!-- Dashboard Container -->
<div class="dashboard-container">
  <!-- Financial Summary Widget -->
  <div class="dashboard-widget" data-widget="financial-summary">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h3 class="mb-0">Financial Summary</h3>
        <div>
          <button type="button" class="btn btn-sm btn-outline-primary" id="start-tour-btn">
            <i class="bi bi-info-circle me-1"></i>Tour
          </button>
        </div>
      </div>
      <div class="card-body">
        <!-- Overall Summary (Larger) -->
        <h4 class="mb-3">Overall Summary</h4>
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="card bg-success text-white">
              <div class="card-body">
                <h5 class="card-title">Total Credits</h5>
                <h2 class="mb-0">₹{{ "%.2f"|format(total_credit) }}</h2>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card bg-danger text-white">
              <div class="card-body">
                <h5 class="card-title">Total Spends</h5>
                <h2 class="mb-0">₹{{ "%.2f"|format(total_debit) }}</h2>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Current Month Summary (Smaller) -->
        <h5 class="mb-3">{{ current_month_name }} Summary</h5>
        <div class="row">
          <div class="col-md-4">
            <div class="card bg-success text-white">
              <div class="card-body py-2">
                <h6 class="card-title">Month Credits</h6>
                <h4 class="mb-0">₹{{ "%.2f"|format(current_month_credits) }}</h4>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card bg-danger text-white">
              <div class="card-body py-2">
                <h6 class="card-title">Month Spends</h6>
                <h4 class="mb-0">₹{{ "%.2f"|format(current_month_debits) }}</h4>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card bg-info text-white">
              <div class="card-body py-2">
                <h6 class="card-title">Avg. Daily Spend</h6>
                <h4 class="mb-0">₹{{ "%.2f"|format(avg_daily_spend) }}</h4>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Budget Overview Widget -->
  <div class="dashboard-widget" data-widget="budget-overview">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h3 class="mb-0">Budget Overview</h3>
        <div>
          <a href="{{ url_for('add_budget') }}" class="btn btn-sm btn-primary">
            <i class="bi bi-plus-circle me-1"></i>Add Budget
          </a>
        </div>
      </div>
      <div class="card-body">
        {% if active_budgets %}
        <div class="row">
          {% for budget in top_budgets %}
          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-header bg-white d-flex justify-content-between align-items-center py-2">
                <h6 class="mb-0">{{ budget.name or budget.category_name }}</h6>
                <span class="badge bg-secondary">{{ budget.period|capitalize }}</span>
              </div>
              <div class="card-body py-2">
                <div class="d-flex justify-content-between mb-1">
                  <small class="text-muted">Budget:</small>
                  <small class="fw-bold">₹{{ budget.amount|format_currency }}</small>
                </div>
                <div class="d-flex justify-content-between mb-1">
                  <small class="text-muted">Spent:</small>
                  <small class="fw-bold">₹{{ budget.spent|format_currency }}</small>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <small class="text-muted">Remaining:</small>
                  <small class="fw-bold">₹{{ budget.remaining|format_currency }}</small>
                </div>
                
                <div class="progress mb-1" style="height: 8px;">
                  {% if budget.percentage >= 100 %}
                  <div class="progress-bar bg-danger" role="progressbar" style="width: 100%;" 
                       aria-valuenow="{{ budget.percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                  {% elif budget.percentage >= budget.notification_threshold %}
                  <div class="progress-bar bg-warning" role="progressbar" style="width: {{ budget.percentage }}%;" 
                       aria-valuenow="{{ budget.percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                  {% else %}
                  <div class="progress-bar" role="progressbar" style="width: {{ budget.percentage }}%; background-color: {{ budget.color }};" 
                       aria-valuenow="{{ budget.percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                  {% endif %}
                </div>
                <div class="d-flex justify-content-between">
                  <small class="text-muted">{{ budget.percentage|format_percentage }} used</small>
                  {% if budget.percentage >= 100 %}
                  <small class="text-danger">Over budget!</small>
                  {% elif budget.percentage >= budget.notification_threshold %}
                  <small class="text-warning">Warning!</small>
                  {% else %}
                  <small class="text-success">On track</small>
                  {% endif %}
                </div>
              </div>
              <div class="card-footer bg-white py-2">
                <a href="{{ url_for('edit_budget', budget_id=budget.id) }}" class="btn btn-sm btn-outline-primary w-100">
                  <i class="bi bi-pencil me-1"></i>Manage
                </a>
              </div>
            </div>
          </div>
          {% endfor %}
          
          {% if active_budgets|length > 3 %}
          <div class="col-12 text-center mt-2">
            <a href="{{ url_for('budget_overview') }}" class="btn btn-sm btn-outline-primary">
              View All {{ active_budgets|length }} Budgets
            </a>
          </div>
          {% endif %}
        </div>
        {% else %}
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i>
          You don't have any active budgets. 
          <a href="{{ url_for('add_budget') }}" class="alert-link">Create a budget</a> to track your spending.
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Side-by-side widgets row -->
  <div class="dashboard-row">
    <!-- Spending by Category Widget -->
    <div class="dashboard-widget" data-widget="spending-category">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h3 class="mb-0">
            <i class="bi bi-pie-chart me-2"></i>Spending by Category
            {% if filter_year != 'all' %}
              <small class="text-muted">
                ({{ filter_month_name }} {{ filter_year }})
              </small>
            {% endif %}
          </h3>
          <div class="d-flex gap-2">
            <a href="{{ url_for('manage_categories') }}" class="btn btn-primary btn-sm">
              <i class="bi bi-tags me-1"></i>Manage Categories
            </a>
            <a href="{{ url_for('enhanced_features.reports_dashboard') }}" class="btn btn-outline-info btn-sm">
              <i class="bi bi-file-earmark-bar-graph me-1"></i>Reports
            </a>
          </div>
        </div>
        <div class="card-body">
          <form id="categoryFilterForm" class="mb-3 row g-2 align-items-center">
            <div class="col-auto">
              <label for="yearFilter" class="visually-hidden">Year</label>
              <select id="yearFilter" name="year" class="form-select form-select-sm" onchange="updateMonthOptions()">
                <option value="all" {% if filter_year == 'all' %}selected{% endif %}>All Years</option>
                {% for year in available_years %}
                  <option value="{{ year }}" {% if filter_year == year|string %}selected{% endif %}>{{ year }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-auto">
              <label for="monthFilter" class="visually-hidden">Month</label>
              <select id="monthFilter" name="month" class="form-select form-select-sm" {% if filter_year == 'all' %}disabled{% endif %}>
                <option value="all" {% if filter_month == 'all' %}selected{% endif %}>All Months</option>
                {% if filter_year != 'all' and filter_year|int in available_months %}
                  {% for month in available_months[filter_year|int] %}
                    <option value="{{ month }}" {% if filter_month == month|string %}selected{% endif %}>
                      {% set month_names = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'] %}
                      {{ month_names[month-1] }}
                    </option>
                  {% endfor %}
                {% endif %}
              </select>
            </div>
            <div class="col-auto">
              <button type="submit" class="btn btn-sm btn-primary">Apply</button>
              {% if filter_year != 'all' or filter_month != 'all' %}
                <a href="{{ url_for('index') }}" class="btn btn-sm btn-outline-secondary">Reset</a>
              {% endif %}
            </div>
          </form>
          
          <canvas id="spendingChart"></canvas>
          
          {% if not spending_categories %}
            <div class="alert alert-info mt-3">
              No spending data available for the selected period.
            </div>
          {% endif %}
        </div>
      </div>
    </div>
    
    <!-- Recent Transactions Widget -->
    <div class="dashboard-widget" data-widget="recent-transactions">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h3 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>Recent Transactions
          </h3>
          <div class="d-flex gap-2">
            <a href="{{ url_for('add_expense') }}" class="btn btn-primary btn-sm">
              <i class="bi bi-plus-circle me-1"></i>Add Transaction
            </a>
            <a href="{{ url_for('recurring_transactions') }}" class="btn btn-outline-primary btn-sm">
              <i class="bi bi-arrow-repeat me-1"></i>Recurring
            </a>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Type</th>
                  <th>Category</th>
                  <th>Amount</th>
                  <th>Description</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for transaction in recent_transactions %}
                <tr>
                  <td>{{ transaction.date.strftime('%Y-%m-%d') }}</td>
                  <td>
                    {% if transaction.transaction_type == 'CR' %}
                      <span class="badge bg-success">Credit</span>
                    {% else %}
                      <span class="badge bg-danger">Debit</span>
                    {% endif %}
                  </td>
                  <td>
                    <form method="POST" action="{{ url_for('update_transaction_category', id=transaction.id) }}" class="d-inline">
                      <select name="category" class="form-select form-select-sm" onchange="this.form.submit()">
                        {% for category in categories %}
                          <option value="{{ category.id }}" {% if category.name == transaction.category_name %}selected{% endif %}>
                            {{ category.name }}
                          </option>
                        {% endfor %}
                      </select>
                    </form>
                  </td>
                  <td>₹{{ "%.2f"|format(transaction.amount) }}</td>
                  <td>{{ transaction.description }}</td>
                  <td>
                    <a href="{{ url_for('delete_expense', id=transaction.id) }}" class="btn btn-sm btn-danger"
                      onclick="return confirm('Are you sure you want to delete this transaction?')">
                      <i class="bi bi-trash"></i>
                    </a>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Dashboard Controls -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <button type="button" class="btn btn-sm btn-outline-info me-2" id="start-tour-btn">
              <i class="bi bi-info-circle me-1"></i>Start Tour
            </button>
          </div>
          <div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showKeyboardShortcutsModal()">
              <i class="bi bi-keyboard me-1"></i>Keyboard Shortcuts
            </button>
            <span class="text-muted ms-2">Press <kbd>Shift</kbd> + <kbd>?</kbd> to show shortcuts anytime</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Available months data from backend
  const availableMonths = {{ available_months|tojson }};
  
  // Function to update month options based on selected year
  function updateMonthOptions() {
    const yearSelect = document.getElementById('yearFilter');
    const monthSelect = document.getElementById('monthFilter');
    const selectedYear = yearSelect.value;
    
    // Clear existing options except the first one (All Months)
    while (monthSelect.options.length > 1) {
      monthSelect.remove(1);
    }
    
    // If "All Years" is selected, disable the month dropdown
    if (selectedYear === 'all') {
      monthSelect.disabled = true;
      monthSelect.value = 'all';
      return;
    }
    
    // Enable the month dropdown
    monthSelect.disabled = false;
    
    // Add month options for the selected year
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    if (availableMonths[selectedYear]) {
      availableMonths[selectedYear].forEach(month => {
        const option = document.createElement('option');
        option.value = month;
        option.textContent = monthNames[month - 1];
        monthSelect.appendChild(option);
      });
    }
  }
  
  // Spending by Category Chart
  const ctx = document.getElementById('spendingChart');
  
  {% if spending_categories %}
  new Chart(ctx, {
    type: 'pie',
    data: {
      labels: {{ spending_categories| tojson }},
      datasets: [{
        data: {{ spending_amounts| tojson }},
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
          '#FF9F40',
          '#8AC24A',
          '#FF5252',
          '#7E57C2',
          '#26A69A'
        ]
      }]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'right',
        },
        tooltip: {
          callbacks: {
            label: function (context) {
              let label = context.label || '';
              let value = context.raw || 0;
              return `${label}: ₹${value.toFixed(2)}`;
            }
          }
        }
      }
    }
  });
  {% endif %}
</script>
{% endblock %}
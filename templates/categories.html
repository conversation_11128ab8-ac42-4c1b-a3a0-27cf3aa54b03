{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
  <div class="col-md-8">
    <div class="card">
      <div class="card-header">
        <h3 class="mb-0">
          <i class="bi bi-tags me-2"></i>Manage Categories
        </h3>
      </div>
      <div class="card-body">
        <!-- Add New Category Form -->
        <form method="POST" class="mb-4">
          <input type="hidden" name="action" value="add">
          <div class="input-group">
            <input type="text" class="form-control" name="name" placeholder="New category name" required>
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-plus-circle me-1"></i>Add Category
            </button>
          </div>
        </form>

        <!-- Categories List -->
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Category Name</th>
                <th>Type</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for category in categories %}
              <tr>
                <td>
                  <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="category_id" value="{{ category.id }}">
                    <div class="input-group">
                      <input type="text" class="form-control" name="name" value="{{ category.name }}" required>
                      <button type="submit" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-check-lg"></i>
                      </button>
                    </div>
                  </form>
                </td>
                <td>
                  {% if category.is_global %}
                  <span class="badge bg-info">Global</span>
                  {% else %}
                  <span class="badge bg-secondary">Custom</span>
                  {% endif %}
                </td>
                <td>
                  {% if not category.is_global %}
                  <form method="POST" class="d-inline"
                    onsubmit="return confirm('Are you sure you want to delete this category?');">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="category_id" value="{{ category.id }}">
                    <button type="submit" class="btn btn-sm btn-danger">
                      <i class="bi bi-trash"></i>
                    </button>
                  </form>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
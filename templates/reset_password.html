{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
  <div class="col-md-6 col-lg-5">
    <div class="card shadow">
      <div class="card-header bg-primary text-white">
        <h3 class="mb-0">
          <i class="bi bi-key me-2"></i>Create New Password
        </h3>
      </div>
      <div class="card-body">
        <p class="mb-4">Please create a new password for your account.</p>
        
        <form method="POST">
          <div class="mb-3">
            <label for="password" class="form-label">New Password</label>
            <input type="password" class="form-control" id="password" name="password" required
                   minlength="8" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$">
            <div class="form-text">
              Password must be at least 8 characters long and include uppercase, lowercase, number, and special character
            </div>
          </div>
          
          <div class="mb-4">
            <label for="confirm_password" class="form-label">Confirm New Password</label>
            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
          </div>
          
          <div class="d-grid mb-3">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-check-circle me-2"></i>Reset Password
            </button>
          </div>
        </form>
        
        <div class="text-center mt-3">
          <a href="{{ url_for('login') }}" class="text-decoration-none">
            <i class="bi bi-arrow-left me-1"></i>Back to Login
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Password confirmation validation
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    form.addEventListener('submit', function(event) {
      if (password.value !== confirmPassword.value) {
        event.preventDefault();
        alert('Passwords do not match!');
      }
    });
  });
</script>
{% endblock %}
{% extends "base.html" %}

{% block content %}
<div class="container">
  <div class="row mb-4">
    <div class="col-md-8">
      <h1 class="mb-3">
        <i class="bi bi-wallet2 me-2"></i>Budget Management
      </h1>
      <p class="text-muted">Create and manage your budgets to keep your spending in check.</p>
    </div>
    <div class="col-md-4 text-md-end d-flex align-items-center justify-content-md-end">
      <a href="{{ url_for('add_budget') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle me-1"></i>New Budget
      </a>
    </div>
  </div>

  {% if budgets %}
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Your Budgets</h5>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Period</th>
                  <th>Budget</th>
                  <th>Spent</th>
                  <th>Remaining</th>
                  <th>Progress</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for budget in budgets %}
                <tr>
                  <td>{{ budget.name or 'Unnamed Budget' }}</td>
                  <td>{{ budget.category_name }}</td>
                  <td>
                    <span class="badge bg-secondary">{{ budget.period|capitalize }}</span>
                    <small class="d-block text-muted mt-1">
                      {{ budget.start_date|format_date }} to {{ budget.end_date|format_date }}
                    </small>
                  </td>
                  <td>₹{{ budget.amount|format_currency }}</td>
                  <td>₹{{ budget.spent|format_currency }}</td>
                  <td>₹{{ budget.remaining|format_currency }}</td>
                  <td style="width: 150px;">
                    <div class="progress" style="height: 10px;">
                      {% if budget.percentage >= 100 %}
                      <div class="progress-bar bg-danger" role="progressbar" style="width: 100%;" 
                           aria-valuenow="{{ budget.percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                      {% elif budget.percentage >= budget.notification_threshold %}
                      <div class="progress-bar bg-warning" role="progressbar" style="width: {{ budget.percentage }}%;" 
                           aria-valuenow="{{ budget.percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                      {% else %}
                      <div class="progress-bar" role="progressbar" style="width: {{ budget.percentage }}%; background-color: {{ budget.color }};" 
                           aria-valuenow="{{ budget.percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                      {% endif %}
                    </div>
                    <small class="d-block text-center mt-1">{{ budget.percentage|format_percentage }}</small>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <a href="{{ url_for('edit_budget', budget_id=budget.id) }}" class="btn btn-outline-primary">
                        <i class="bi bi-pencil"></i>
                      </a>
                      <a href="{{ url_for('delete_budget', budget_id=budget.id) }}" class="btn btn-outline-danger" 
                         onclick="return confirm('Are you sure you want to delete this budget?');">
                        <i class="bi bi-trash"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  {% else %}
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm mb-4">
        <div class="card-body text-center py-5">
          <i class="bi bi-wallet2 display-1 text-muted mb-3"></i>
          <h3>No Budgets Yet</h3>
          <p class="text-muted">You haven't created any budgets yet. Create your first budget to start tracking your spending.</p>
          <a href="{{ url_for('add_budget') }}" class="btn btn-primary mt-2">
            <i class="bi bi-plus-circle me-1"></i>Create Your First Budget
          </a>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Budget Tips</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="d-flex mb-3">
                <div class="me-3">
                  <i class="bi bi-piggy-bank text-primary" style="font-size: 2rem;"></i>
                </div>
                <div>
                  <h6>50/30/20 Rule</h6>
                  <p class="text-muted small mb-0">Allocate 50% for needs, 30% for wants, and 20% for savings.</p>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="d-flex mb-3">
                <div class="me-3">
                  <i class="bi bi-graph-up-arrow text-primary" style="font-size: 2rem;"></i>
                </div>
                <div>
                  <h6>Track Regularly</h6>
                  <p class="text-muted small mb-0">Review your budgets weekly to stay on track with your goals.</p>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="d-flex mb-3">
                <div class="me-3">
                  <i class="bi bi-calendar-check text-primary" style="font-size: 2rem;"></i>
                </div>
                <div>
                  <h6>Set Realistic Goals</h6>
                  <p class="text-muted small mb-0">Start with achievable budgets and adjust as you learn your spending habits.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Offline - Money Tracker</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f7fa;
      color: #333;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      justify-content: center;
      align-items: center;
      padding: 20px;
    }
    
    .offline-container {
      max-width: 500px;
      text-align: center;
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .offline-icon {
      font-size: 4rem;
      color: #4a6bff;
      margin-bottom: 20px;
    }
    
    h1 {
      font-weight: 600;
      margin-bottom: 20px;
    }
    
    p {
      margin-bottom: 25px;
      color: #6c757d;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #4a6bff, #6a5acd);
      border: none;
      padding: 10px 20px;
      border-radius: 10px;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  </style>
</head>
<body>
  <div class="offline-container">
    <div class="offline-icon">
      <i class="bi bi-wifi-off"></i>
    </div>
    <h1>You're Offline</h1>
    <p>It looks like you've lost your internet connection. Some features of Money Tracker may be limited while you're offline.</p>
    <p>You can still view cached data and add new transactions, which will sync when you're back online.</p>
    <button class="btn btn-primary" onclick="window.location.reload()">
      <i class="bi bi-arrow-clockwise me-2"></i>Try Again
    </button>
  </div>
  
  <script>
    // Check if we're back online
    window.addEventListener('online', () => {
      window.location.reload();
    });
  </script>
</body>
</html>
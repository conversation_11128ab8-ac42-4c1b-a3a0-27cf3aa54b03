"""
    pygments.lexers._vim_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    This file is autogenerated by scripts/get_vimkw.py

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

# Split up in multiple functions so it's importable by jython, which has a
# per-method size limit.

def _getauto():
    var = (
        ('BufAdd','BufAdd'),
        ('BufCreate','BufCreate'),
        ('BufDelete','BufDelete'),
        ('BufEnter','BufEnter'),
        ('BufFilePost','BufFilePost'),
        ('BufFilePre','BufFilePre'),
        ('BufHidden','BufHidden'),
        ('BufLeave','BufLeave'),
        ('BufNew','BufNew'),
        ('BufNewFile','BufNewFile'),
        ('BufRead','BufRead'),
        ('BufReadCmd','BufReadCmd'),
        ('BufReadPost','BufReadPost'),
        ('BufReadPre','BufReadPre'),
        ('BufUnload','BufUnload'),
        ('BufWinEnter','BufWinEnter'),
        ('BufWinLeave','BufWinLeave'),
        ('BufWipeout','BufWipeout'),
        ('BufWrite','BufWrite'),
        ('BufWriteCmd','BufWriteCmd'),
        ('BufWritePost','BufWritePost'),
        ('BufWritePre','BufWritePre'),
        ('Cmd','Cmd'),
        ('CmdwinEnter','CmdwinEnter'),
        ('CmdwinLeave','CmdwinLeave'),
        ('ColorScheme','ColorScheme'),
        ('CompleteDone','CompleteDone'),
        ('CursorHold','CursorHold'),
        ('CursorHoldI','CursorHoldI'),
        ('CursorMoved','CursorMoved'),
        ('CursorMovedI','CursorMovedI'),
        ('EncodingChanged','EncodingChanged'),
        ('FileAppendCmd','FileAppendCmd'),
        ('FileAppendPost','FileAppendPost'),
        ('FileAppendPre','FileAppendPre'),
        ('FileChangedRO','FileChangedRO'),
        ('FileChangedShell','FileChangedShell'),
        ('FileChangedShellPost','FileChangedShellPost'),
        ('FileEncoding','FileEncoding'),
        ('FileReadCmd','FileReadCmd'),
        ('FileReadPost','FileReadPost'),
        ('FileReadPre','FileReadPre'),
        ('FileType','FileType'),
        ('FileWriteCmd','FileWriteCmd'),
        ('FileWritePost','FileWritePost'),
        ('FileWritePre','FileWritePre'),
        ('FilterReadPost','FilterReadPost'),
        ('FilterReadPre','FilterReadPre'),
        ('FilterWritePost','FilterWritePost'),
        ('FilterWritePre','FilterWritePre'),
        ('FocusGained','FocusGained'),
        ('FocusLost','FocusLost'),
        ('FuncUndefined','FuncUndefined'),
        ('GUIEnter','GUIEnter'),
        ('GUIFailed','GUIFailed'),
        ('InsertChange','InsertChange'),
        ('InsertCharPre','InsertCharPre'),
        ('InsertEnter','InsertEnter'),
        ('InsertLeave','InsertLeave'),
        ('MenuPopup','MenuPopup'),
        ('QuickFixCmdPost','QuickFixCmdPost'),
        ('QuickFixCmdPre','QuickFixCmdPre'),
        ('QuitPre','QuitPre'),
        ('RemoteReply','RemoteReply'),
        ('SessionLoadPost','SessionLoadPost'),
        ('ShellCmdPost','ShellCmdPost'),
        ('ShellFilterPost','ShellFilterPost'),
        ('SourceCmd','SourceCmd'),
        ('SourcePre','SourcePre'),
        ('SpellFileMissing','SpellFileMissing'),
        ('StdinReadPost','StdinReadPost'),
        ('StdinReadPre','StdinReadPre'),
        ('SwapExists','SwapExists'),
        ('Syntax','Syntax'),
        ('TabEnter','TabEnter'),
        ('TabLeave','TabLeave'),
        ('TermChanged','TermChanged'),
        ('TermResponse','TermResponse'),
        ('TextChanged','TextChanged'),
        ('TextChangedI','TextChangedI'),
        ('User','User'),
        ('UserGettingBored','UserGettingBored'),
        ('VimEnter','VimEnter'),
        ('VimLeave','VimLeave'),
        ('VimLeavePre','VimLeavePre'),
        ('VimResized','VimResized'),
        ('WinEnter','WinEnter'),
        ('WinLeave','WinLeave'),
        ('event','event'),
    )
    return var
auto = _getauto()

def _getcommand():
    var = (
        ('a','a'),
        ('ab','ab'),
        ('abc','abclear'),
        ('abo','aboveleft'),
        ('al','all'),
        ('ar','ar'),
        ('ar','args'),
        ('arga','argadd'),
        ('argd','argdelete'),
        ('argdo','argdo'),
        ('arge','argedit'),
        ('argg','argglobal'),
        ('argl','arglocal'),
        ('argu','argument'),
        ('as','ascii'),
        ('au','au'),
        ('b','buffer'),
        ('bN','bNext'),
        ('ba','ball'),
        ('bad','badd'),
        ('bd','bdelete'),
        ('bel','belowright'),
        ('bf','bfirst'),
        ('bl','blast'),
        ('bm','bmodified'),
        ('bn','bnext'),
        ('bo','botright'),
        ('bp','bprevious'),
        ('br','br'),
        ('br','brewind'),
        ('brea','break'),
        ('breaka','breakadd'),
        ('breakd','breakdel'),
        ('breakl','breaklist'),
        ('bro','browse'),
        ('bu','bu'),
        ('buf','buf'),
        ('bufdo','bufdo'),
        ('buffers','buffers'),
        ('bun','bunload'),
        ('bw','bwipeout'),
        ('c','c'),
        ('c','change'),
        ('cN','cN'),
        ('cN','cNext'),
        ('cNf','cNf'),
        ('cNf','cNfile'),
        ('cabc','cabclear'),
        ('cad','cad'),
        ('cad','caddexpr'),
        ('caddb','caddbuffer'),
        ('caddf','caddfile'),
        ('cal','call'),
        ('cat','catch'),
        ('cb','cbuffer'),
        ('cc','cc'),
        ('ccl','cclose'),
        ('cd','cd'),
        ('ce','center'),
        ('cex','cexpr'),
        ('cf','cfile'),
        ('cfir','cfirst'),
        ('cg','cgetfile'),
        ('cgetb','cgetbuffer'),
        ('cgete','cgetexpr'),
        ('changes','changes'),
        ('chd','chdir'),
        ('che','checkpath'),
        ('checkt','checktime'),
        ('cl','cl'),
        ('cl','clist'),
        ('cla','clast'),
        ('clo','close'),
        ('cmapc','cmapclear'),
        ('cn','cn'),
        ('cn','cnext'),
        ('cnew','cnewer'),
        ('cnf','cnf'),
        ('cnf','cnfile'),
        ('co','copy'),
        ('col','colder'),
        ('colo','colorscheme'),
        ('com','com'),
        ('comc','comclear'),
        ('comp','compiler'),
        ('con','con'),
        ('con','continue'),
        ('conf','confirm'),
        ('cope','copen'),
        ('cp','cprevious'),
        ('cpf','cpfile'),
        ('cq','cquit'),
        ('cr','crewind'),
        ('cs','cs'),
        ('cscope','cscope'),
        ('cstag','cstag'),
        ('cuna','cunabbrev'),
        ('cw','cwindow'),
        ('d','d'),
        ('d','delete'),
        ('de','de'),
        ('debug','debug'),
        ('debugg','debuggreedy'),
        ('del','del'),
        ('delc','delcommand'),
        ('delel','delel'),
        ('delep','delep'),
        ('deletel','deletel'),
        ('deletep','deletep'),
        ('deletl','deletl'),
        ('deletp','deletp'),
        ('delf','delf'),
        ('delf','delfunction'),
        ('dell','dell'),
        ('delm','delmarks'),
        ('delp','delp'),
        ('dep','dep'),
        ('di','di'),
        ('di','display'),
        ('diffg','diffget'),
        ('diffo','diffoff'),
        ('diffp','diffpatch'),
        ('diffpu','diffput'),
        ('diffs','diffsplit'),
        ('difft','diffthis'),
        ('diffu','diffupdate'),
        ('dig','dig'),
        ('dig','digraphs'),
        ('dir','dir'),
        ('dj','djump'),
        ('dl','dl'),
        ('dli','dlist'),
        ('do','do'),
        ('doau','doau'),
        ('dp','dp'),
        ('dr','drop'),
        ('ds','dsearch'),
        ('dsp','dsplit'),
        ('e','e'),
        ('e','edit'),
        ('ea','ea'),
        ('earlier','earlier'),
        ('ec','ec'),
        ('echoe','echoerr'),
        ('echom','echomsg'),
        ('echon','echon'),
        ('el','else'),
        ('elsei','elseif'),
        ('em','emenu'),
        ('en','en'),
        ('en','endif'),
        ('endf','endf'),
        ('endf','endfunction'),
        ('endfo','endfor'),
        ('endfun','endfun'),
        ('endt','endtry'),
        ('endw','endwhile'),
        ('ene','enew'),
        ('ex','ex'),
        ('exi','exit'),
        ('exu','exusage'),
        ('f','f'),
        ('f','file'),
        ('files','files'),
        ('filet','filet'),
        ('filetype','filetype'),
        ('fin','fin'),
        ('fin','find'),
        ('fina','finally'),
        ('fini','finish'),
        ('fir','first'),
        ('fix','fixdel'),
        ('fo','fold'),
        ('foldc','foldclose'),
        ('foldd','folddoopen'),
        ('folddoc','folddoclosed'),
        ('foldo','foldopen'),
        ('for','for'),
        ('fu','fu'),
        ('fu','function'),
        ('fun','fun'),
        ('g','g'),
        ('go','goto'),
        ('gr','grep'),
        ('grepa','grepadd'),
        ('gui','gui'),
        ('gvim','gvim'),
        ('h','h'),
        ('h','help'),
        ('ha','hardcopy'),
        ('helpf','helpfind'),
        ('helpg','helpgrep'),
        ('helpt','helptags'),
        ('hi','hi'),
        ('hid','hide'),
        ('his','history'),
        ('i','i'),
        ('ia','ia'),
        ('iabc','iabclear'),
        ('if','if'),
        ('ij','ijump'),
        ('il','ilist'),
        ('imapc','imapclear'),
        ('in','in'),
        ('intro','intro'),
        ('is','isearch'),
        ('isp','isplit'),
        ('iuna','iunabbrev'),
        ('j','join'),
        ('ju','jumps'),
        ('k','k'),
        ('kee','keepmarks'),
        ('keepa','keepa'),
        ('keepalt','keepalt'),
        ('keepj','keepjumps'),
        ('keepp','keeppatterns'),
        ('l','l'),
        ('l','list'),
        ('lN','lN'),
        ('lN','lNext'),
        ('lNf','lNf'),
        ('lNf','lNfile'),
        ('la','la'),
        ('la','last'),
        ('lad','lad'),
        ('lad','laddexpr'),
        ('laddb','laddbuffer'),
        ('laddf','laddfile'),
        ('lan','lan'),
        ('lan','language'),
        ('lat','lat'),
        ('later','later'),
        ('lb','lbuffer'),
        ('lc','lcd'),
        ('lch','lchdir'),
        ('lcl','lclose'),
        ('lcs','lcs'),
        ('lcscope','lcscope'),
        ('le','left'),
        ('lefta','leftabove'),
        ('lex','lexpr'),
        ('lf','lfile'),
        ('lfir','lfirst'),
        ('lg','lgetfile'),
        ('lgetb','lgetbuffer'),
        ('lgete','lgetexpr'),
        ('lgr','lgrep'),
        ('lgrepa','lgrepadd'),
        ('lh','lhelpgrep'),
        ('ll','ll'),
        ('lla','llast'),
        ('lli','llist'),
        ('lmak','lmake'),
        ('lmapc','lmapclear'),
        ('lne','lne'),
        ('lne','lnext'),
        ('lnew','lnewer'),
        ('lnf','lnf'),
        ('lnf','lnfile'),
        ('lo','lo'),
        ('lo','loadview'),
        ('loadk','loadk'),
        ('loadkeymap','loadkeymap'),
        ('loc','lockmarks'),
        ('lockv','lockvar'),
        ('lol','lolder'),
        ('lop','lopen'),
        ('lp','lprevious'),
        ('lpf','lpfile'),
        ('lr','lrewind'),
        ('ls','ls'),
        ('lt','ltag'),
        ('lua','lua'),
        ('luado','luado'),
        ('luafile','luafile'),
        ('lv','lvimgrep'),
        ('lvimgrepa','lvimgrepadd'),
        ('lw','lwindow'),
        ('m','move'),
        ('ma','ma'),
        ('ma','mark'),
        ('mak','make'),
        ('marks','marks'),
        ('mat','match'),
        ('menut','menut'),
        ('menut','menutranslate'),
        ('mes','mes'),
        ('messages','messages'),
        ('mk','mk'),
        ('mk','mkexrc'),
        ('mks','mksession'),
        ('mksp','mkspell'),
        ('mkv','mkv'),
        ('mkv','mkvimrc'),
        ('mkvie','mkview'),
        ('mo','mo'),
        ('mod','mode'),
        ('mz','mz'),
        ('mz','mzscheme'),
        ('mzf','mzfile'),
        ('n','n'),
        ('n','next'),
        ('nb','nbkey'),
        ('nbc','nbclose'),
        ('nbs','nbstart'),
        ('ne','ne'),
        ('new','new'),
        ('nmapc','nmapclear'),
        ('noa','noa'),
        ('noautocmd','noautocmd'),
        ('noh','nohlsearch'),
        ('nu','number'),
        ('o','o'),
        ('o','open'),
        ('ol','oldfiles'),
        ('omapc','omapclear'),
        ('on','only'),
        ('opt','options'),
        ('ownsyntax','ownsyntax'),
        ('p','p'),
        ('p','print'),
        ('pc','pclose'),
        ('pe','pe'),
        ('pe','perl'),
        ('ped','pedit'),
        ('perld','perldo'),
        ('po','pop'),
        ('popu','popu'),
        ('popu','popup'),
        ('pp','ppop'),
        ('pr','pr'),
        ('pre','preserve'),
        ('prev','previous'),
        ('pro','pro'),
        ('prof','profile'),
        ('profd','profdel'),
        ('promptf','promptfind'),
        ('promptr','promptrepl'),
        ('ps','psearch'),
        ('ptN','ptN'),
        ('ptN','ptNext'),
        ('pta','ptag'),
        ('ptf','ptfirst'),
        ('ptj','ptjump'),
        ('ptl','ptlast'),
        ('ptn','ptn'),
        ('ptn','ptnext'),
        ('ptp','ptprevious'),
        ('ptr','ptrewind'),
        ('pts','ptselect'),
        ('pu','put'),
        ('pw','pwd'),
        ('py','py'),
        ('py','python'),
        ('py3','py3'),
        ('py3','py3'),
        ('py3do','py3do'),
        ('pydo','pydo'),
        ('pyf','pyfile'),
        ('python3','python3'),
        ('q','q'),
        ('q','quit'),
        ('qa','qall'),
        ('quita','quitall'),
        ('r','r'),
        ('r','read'),
        ('re','re'),
        ('rec','recover'),
        ('red','red'),
        ('red','redo'),
        ('redi','redir'),
        ('redr','redraw'),
        ('redraws','redrawstatus'),
        ('reg','registers'),
        ('res','resize'),
        ('ret','retab'),
        ('retu','return'),
        ('rew','rewind'),
        ('ri','right'),
        ('rightb','rightbelow'),
        ('ru','ru'),
        ('ru','runtime'),
        ('rub','ruby'),
        ('rubyd','rubydo'),
        ('rubyf','rubyfile'),
        ('rundo','rundo'),
        ('rv','rviminfo'),
        ('sN','sNext'),
        ('sa','sargument'),
        ('sal','sall'),
        ('san','sandbox'),
        ('sav','saveas'),
        ('sb','sbuffer'),
        ('sbN','sbNext'),
        ('sba','sball'),
        ('sbf','sbfirst'),
        ('sbl','sblast'),
        ('sbm','sbmodified'),
        ('sbn','sbnext'),
        ('sbp','sbprevious'),
        ('sbr','sbrewind'),
        ('scrip','scrip'),
        ('scrip','scriptnames'),
        ('scripte','scriptencoding'),
        ('scs','scs'),
        ('scscope','scscope'),
        ('se','set'),
        ('setf','setfiletype'),
        ('setg','setglobal'),
        ('setl','setlocal'),
        ('sf','sfind'),
        ('sfir','sfirst'),
        ('sh','shell'),
        ('si','si'),
        ('sig','sig'),
        ('sign','sign'),
        ('sil','silent'),
        ('sim','simalt'),
        ('sl','sl'),
        ('sl','sleep'),
        ('sla','slast'),
        ('sm','smagic'),
        ('sm','smap'),
        ('sme','sme'),
        ('smenu','smenu'),
        ('sn','snext'),
        ('sni','sniff'),
        ('sno','snomagic'),
        ('snoreme','snoreme'),
        ('snoremenu','snoremenu'),
        ('so','so'),
        ('so','source'),
        ('sor','sort'),
        ('sp','split'),
        ('spe','spe'),
        ('spe','spellgood'),
        ('spelld','spelldump'),
        ('spelli','spellinfo'),
        ('spellr','spellrepall'),
        ('spellu','spellundo'),
        ('spellw','spellwrong'),
        ('spr','sprevious'),
        ('sre','srewind'),
        ('st','st'),
        ('st','stop'),
        ('sta','stag'),
        ('star','star'),
        ('star','startinsert'),
        ('start','start'),
        ('startg','startgreplace'),
        ('startr','startreplace'),
        ('stj','stjump'),
        ('stopi','stopinsert'),
        ('sts','stselect'),
        ('sun','sunhide'),
        ('sunme','sunme'),
        ('sunmenu','sunmenu'),
        ('sus','suspend'),
        ('sv','sview'),
        ('sw','swapname'),
        ('sy','sy'),
        ('syn','syn'),
        ('sync','sync'),
        ('syncbind','syncbind'),
        ('syntime','syntime'),
        ('t','t'),
        ('tN','tN'),
        ('tN','tNext'),
        ('ta','ta'),
        ('ta','tag'),
        ('tab','tab'),
        ('tabN','tabN'),
        ('tabN','tabNext'),
        ('tabc','tabclose'),
        ('tabd','tabdo'),
        ('tabe','tabedit'),
        ('tabf','tabfind'),
        ('tabfir','tabfirst'),
        ('tabl','tablast'),
        ('tabm','tabmove'),
        ('tabn','tabnext'),
        ('tabnew','tabnew'),
        ('tabo','tabonly'),
        ('tabp','tabprevious'),
        ('tabr','tabrewind'),
        ('tabs','tabs'),
        ('tags','tags'),
        ('tc','tcl'),
        ('tcld','tcldo'),
        ('tclf','tclfile'),
        ('te','tearoff'),
        ('tf','tfirst'),
        ('th','throw'),
        ('tj','tjump'),
        ('tl','tlast'),
        ('tm','tm'),
        ('tm','tmenu'),
        ('tn','tn'),
        ('tn','tnext'),
        ('to','topleft'),
        ('tp','tprevious'),
        ('tr','tr'),
        ('tr','trewind'),
        ('try','try'),
        ('ts','tselect'),
        ('tu','tu'),
        ('tu','tunmenu'),
        ('u','u'),
        ('u','undo'),
        ('un','un'),
        ('una','unabbreviate'),
        ('undoj','undojoin'),
        ('undol','undolist'),
        ('unh','unhide'),
        ('unl','unl'),
        ('unlo','unlockvar'),
        ('uns','unsilent'),
        ('up','update'),
        ('v','v'),
        ('ve','ve'),
        ('ve','version'),
        ('verb','verbose'),
        ('vert','vertical'),
        ('vi','vi'),
        ('vi','visual'),
        ('vie','view'),
        ('vim','vimgrep'),
        ('vimgrepa','vimgrepadd'),
        ('viu','viusage'),
        ('vmapc','vmapclear'),
        ('vne','vnew'),
        ('vs','vsplit'),
        ('w','w'),
        ('w','write'),
        ('wN','wNext'),
        ('wa','wall'),
        ('wh','while'),
        ('win','win'),
        ('win','winsize'),
        ('winc','wincmd'),
        ('windo','windo'),
        ('winp','winpos'),
        ('wn','wnext'),
        ('wp','wprevious'),
        ('wq','wq'),
        ('wqa','wqall'),
        ('ws','wsverb'),
        ('wundo','wundo'),
        ('wv','wviminfo'),
        ('x','x'),
        ('x','xit'),
        ('xa','xall'),
        ('xmapc','xmapclear'),
        ('xme','xme'),
        ('xmenu','xmenu'),
        ('xnoreme','xnoreme'),
        ('xnoremenu','xnoremenu'),
        ('xunme','xunme'),
        ('xunmenu','xunmenu'),
        ('xwininfo','xwininfo'),
        ('y','yank'),
    )
    return var
command = _getcommand()

def _getoption():
    var = (
        ('acd','acd'),
        ('ai','ai'),
        ('akm','akm'),
        ('al','al'),
        ('aleph','aleph'),
        ('allowrevins','allowrevins'),
        ('altkeymap','altkeymap'),
        ('ambiwidth','ambiwidth'),
        ('ambw','ambw'),
        ('anti','anti'),
        ('antialias','antialias'),
        ('ar','ar'),
        ('arab','arab'),
        ('arabic','arabic'),
        ('arabicshape','arabicshape'),
        ('ari','ari'),
        ('arshape','arshape'),
        ('autochdir','autochdir'),
        ('autoindent','autoindent'),
        ('autoread','autoread'),
        ('autowrite','autowrite'),
        ('autowriteall','autowriteall'),
        ('aw','aw'),
        ('awa','awa'),
        ('background','background'),
        ('backspace','backspace'),
        ('backup','backup'),
        ('backupcopy','backupcopy'),
        ('backupdir','backupdir'),
        ('backupext','backupext'),
        ('backupskip','backupskip'),
        ('balloondelay','balloondelay'),
        ('ballooneval','ballooneval'),
        ('balloonexpr','balloonexpr'),
        ('bdir','bdir'),
        ('bdlay','bdlay'),
        ('beval','beval'),
        ('bex','bex'),
        ('bexpr','bexpr'),
        ('bg','bg'),
        ('bh','bh'),
        ('bin','bin'),
        ('binary','binary'),
        ('biosk','biosk'),
        ('bioskey','bioskey'),
        ('bk','bk'),
        ('bkc','bkc'),
        ('bl','bl'),
        ('bomb','bomb'),
        ('breakat','breakat'),
        ('brk','brk'),
        ('browsedir','browsedir'),
        ('bs','bs'),
        ('bsdir','bsdir'),
        ('bsk','bsk'),
        ('bt','bt'),
        ('bufhidden','bufhidden'),
        ('buflisted','buflisted'),
        ('buftype','buftype'),
        ('casemap','casemap'),
        ('cb','cb'),
        ('cc','cc'),
        ('ccv','ccv'),
        ('cd','cd'),
        ('cdpath','cdpath'),
        ('cedit','cedit'),
        ('cf','cf'),
        ('cfu','cfu'),
        ('ch','ch'),
        ('charconvert','charconvert'),
        ('ci','ci'),
        ('cin','cin'),
        ('cindent','cindent'),
        ('cink','cink'),
        ('cinkeys','cinkeys'),
        ('cino','cino'),
        ('cinoptions','cinoptions'),
        ('cinw','cinw'),
        ('cinwords','cinwords'),
        ('clipboard','clipboard'),
        ('cmdheight','cmdheight'),
        ('cmdwinheight','cmdwinheight'),
        ('cmp','cmp'),
        ('cms','cms'),
        ('co','co'),
        ('cocu','cocu'),
        ('cole','cole'),
        ('colorcolumn','colorcolumn'),
        ('columns','columns'),
        ('com','com'),
        ('comments','comments'),
        ('commentstring','commentstring'),
        ('compatible','compatible'),
        ('complete','complete'),
        ('completefunc','completefunc'),
        ('completeopt','completeopt'),
        ('concealcursor','concealcursor'),
        ('conceallevel','conceallevel'),
        ('confirm','confirm'),
        ('consk','consk'),
        ('conskey','conskey'),
        ('copyindent','copyindent'),
        ('cot','cot'),
        ('cp','cp'),
        ('cpo','cpo'),
        ('cpoptions','cpoptions'),
        ('cpt','cpt'),
        ('crb','crb'),
        ('cryptmethod','cryptmethod'),
        ('cscopepathcomp','cscopepathcomp'),
        ('cscopeprg','cscopeprg'),
        ('cscopequickfix','cscopequickfix'),
        ('cscoperelative','cscoperelative'),
        ('cscopetag','cscopetag'),
        ('cscopetagorder','cscopetagorder'),
        ('cscopeverbose','cscopeverbose'),
        ('cspc','cspc'),
        ('csprg','csprg'),
        ('csqf','csqf'),
        ('csre','csre'),
        ('cst','cst'),
        ('csto','csto'),
        ('csverb','csverb'),
        ('cuc','cuc'),
        ('cul','cul'),
        ('cursorbind','cursorbind'),
        ('cursorcolumn','cursorcolumn'),
        ('cursorline','cursorline'),
        ('cwh','cwh'),
        ('debug','debug'),
        ('deco','deco'),
        ('def','def'),
        ('define','define'),
        ('delcombine','delcombine'),
        ('dex','dex'),
        ('dg','dg'),
        ('dict','dict'),
        ('dictionary','dictionary'),
        ('diff','diff'),
        ('diffexpr','diffexpr'),
        ('diffopt','diffopt'),
        ('digraph','digraph'),
        ('dip','dip'),
        ('dir','dir'),
        ('directory','directory'),
        ('display','display'),
        ('dy','dy'),
        ('ea','ea'),
        ('ead','ead'),
        ('eadirection','eadirection'),
        ('eb','eb'),
        ('ed','ed'),
        ('edcompatible','edcompatible'),
        ('ef','ef'),
        ('efm','efm'),
        ('ei','ei'),
        ('ek','ek'),
        ('enc','enc'),
        ('encoding','encoding'),
        ('endofline','endofline'),
        ('eol','eol'),
        ('ep','ep'),
        ('equalalways','equalalways'),
        ('equalprg','equalprg'),
        ('errorbells','errorbells'),
        ('errorfile','errorfile'),
        ('errorformat','errorformat'),
        ('esckeys','esckeys'),
        ('et','et'),
        ('eventignore','eventignore'),
        ('ex','ex'),
        ('expandtab','expandtab'),
        ('exrc','exrc'),
        ('fcl','fcl'),
        ('fcs','fcs'),
        ('fdc','fdc'),
        ('fde','fde'),
        ('fdi','fdi'),
        ('fdl','fdl'),
        ('fdls','fdls'),
        ('fdm','fdm'),
        ('fdn','fdn'),
        ('fdo','fdo'),
        ('fdt','fdt'),
        ('fen','fen'),
        ('fenc','fenc'),
        ('fencs','fencs'),
        ('fex','fex'),
        ('ff','ff'),
        ('ffs','ffs'),
        ('fic','fic'),
        ('fileencoding','fileencoding'),
        ('fileencodings','fileencodings'),
        ('fileformat','fileformat'),
        ('fileformats','fileformats'),
        ('fileignorecase','fileignorecase'),
        ('filetype','filetype'),
        ('fillchars','fillchars'),
        ('fk','fk'),
        ('fkmap','fkmap'),
        ('flp','flp'),
        ('fml','fml'),
        ('fmr','fmr'),
        ('fo','fo'),
        ('foldclose','foldclose'),
        ('foldcolumn','foldcolumn'),
        ('foldenable','foldenable'),
        ('foldexpr','foldexpr'),
        ('foldignore','foldignore'),
        ('foldlevel','foldlevel'),
        ('foldlevelstart','foldlevelstart'),
        ('foldmarker','foldmarker'),
        ('foldmethod','foldmethod'),
        ('foldminlines','foldminlines'),
        ('foldnestmax','foldnestmax'),
        ('foldopen','foldopen'),
        ('foldtext','foldtext'),
        ('formatexpr','formatexpr'),
        ('formatlistpat','formatlistpat'),
        ('formatoptions','formatoptions'),
        ('formatprg','formatprg'),
        ('fp','fp'),
        ('fs','fs'),
        ('fsync','fsync'),
        ('ft','ft'),
        ('gcr','gcr'),
        ('gd','gd'),
        ('gdefault','gdefault'),
        ('gfm','gfm'),
        ('gfn','gfn'),
        ('gfs','gfs'),
        ('gfw','gfw'),
        ('ghr','ghr'),
        ('go','go'),
        ('gp','gp'),
        ('grepformat','grepformat'),
        ('grepprg','grepprg'),
        ('gtl','gtl'),
        ('gtt','gtt'),
        ('guicursor','guicursor'),
        ('guifont','guifont'),
        ('guifontset','guifontset'),
        ('guifontwide','guifontwide'),
        ('guiheadroom','guiheadroom'),
        ('guioptions','guioptions'),
        ('guipty','guipty'),
        ('guitablabel','guitablabel'),
        ('guitabtooltip','guitabtooltip'),
        ('helpfile','helpfile'),
        ('helpheight','helpheight'),
        ('helplang','helplang'),
        ('hf','hf'),
        ('hh','hh'),
        ('hi','hi'),
        ('hid','hid'),
        ('hidden','hidden'),
        ('highlight','highlight'),
        ('history','history'),
        ('hk','hk'),
        ('hkmap','hkmap'),
        ('hkmapp','hkmapp'),
        ('hkp','hkp'),
        ('hl','hl'),
        ('hlg','hlg'),
        ('hls','hls'),
        ('hlsearch','hlsearch'),
        ('ic','ic'),
        ('icon','icon'),
        ('iconstring','iconstring'),
        ('ignorecase','ignorecase'),
        ('im','im'),
        ('imactivatefunc','imactivatefunc'),
        ('imactivatekey','imactivatekey'),
        ('imaf','imaf'),
        ('imak','imak'),
        ('imc','imc'),
        ('imcmdline','imcmdline'),
        ('imd','imd'),
        ('imdisable','imdisable'),
        ('imi','imi'),
        ('iminsert','iminsert'),
        ('ims','ims'),
        ('imsearch','imsearch'),
        ('imsf','imsf'),
        ('imstatusfunc','imstatusfunc'),
        ('inc','inc'),
        ('include','include'),
        ('includeexpr','includeexpr'),
        ('incsearch','incsearch'),
        ('inde','inde'),
        ('indentexpr','indentexpr'),
        ('indentkeys','indentkeys'),
        ('indk','indk'),
        ('inex','inex'),
        ('inf','inf'),
        ('infercase','infercase'),
        ('inoremap','inoremap'),
        ('insertmode','insertmode'),
        ('invacd','invacd'),
        ('invai','invai'),
        ('invakm','invakm'),
        ('invallowrevins','invallowrevins'),
        ('invaltkeymap','invaltkeymap'),
        ('invanti','invanti'),
        ('invantialias','invantialias'),
        ('invar','invar'),
        ('invarab','invarab'),
        ('invarabic','invarabic'),
        ('invarabicshape','invarabicshape'),
        ('invari','invari'),
        ('invarshape','invarshape'),
        ('invautochdir','invautochdir'),
        ('invautoindent','invautoindent'),
        ('invautoread','invautoread'),
        ('invautowrite','invautowrite'),
        ('invautowriteall','invautowriteall'),
        ('invaw','invaw'),
        ('invawa','invawa'),
        ('invbackup','invbackup'),
        ('invballooneval','invballooneval'),
        ('invbeval','invbeval'),
        ('invbin','invbin'),
        ('invbinary','invbinary'),
        ('invbiosk','invbiosk'),
        ('invbioskey','invbioskey'),
        ('invbk','invbk'),
        ('invbl','invbl'),
        ('invbomb','invbomb'),
        ('invbuflisted','invbuflisted'),
        ('invcf','invcf'),
        ('invci','invci'),
        ('invcin','invcin'),
        ('invcindent','invcindent'),
        ('invcompatible','invcompatible'),
        ('invconfirm','invconfirm'),
        ('invconsk','invconsk'),
        ('invconskey','invconskey'),
        ('invcopyindent','invcopyindent'),
        ('invcp','invcp'),
        ('invcrb','invcrb'),
        ('invcscoperelative','invcscoperelative'),
        ('invcscopetag','invcscopetag'),
        ('invcscopeverbose','invcscopeverbose'),
        ('invcsre','invcsre'),
        ('invcst','invcst'),
        ('invcsverb','invcsverb'),
        ('invcuc','invcuc'),
        ('invcul','invcul'),
        ('invcursorbind','invcursorbind'),
        ('invcursorcolumn','invcursorcolumn'),
        ('invcursorline','invcursorline'),
        ('invdeco','invdeco'),
        ('invdelcombine','invdelcombine'),
        ('invdg','invdg'),
        ('invdiff','invdiff'),
        ('invdigraph','invdigraph'),
        ('invea','invea'),
        ('inveb','inveb'),
        ('inved','inved'),
        ('invedcompatible','invedcompatible'),
        ('invek','invek'),
        ('invendofline','invendofline'),
        ('inveol','inveol'),
        ('invequalalways','invequalalways'),
        ('inverrorbells','inverrorbells'),
        ('invesckeys','invesckeys'),
        ('invet','invet'),
        ('invex','invex'),
        ('invexpandtab','invexpandtab'),
        ('invexrc','invexrc'),
        ('invfen','invfen'),
        ('invfic','invfic'),
        ('invfileignorecase','invfileignorecase'),
        ('invfk','invfk'),
        ('invfkmap','invfkmap'),
        ('invfoldenable','invfoldenable'),
        ('invgd','invgd'),
        ('invgdefault','invgdefault'),
        ('invguipty','invguipty'),
        ('invhid','invhid'),
        ('invhidden','invhidden'),
        ('invhk','invhk'),
        ('invhkmap','invhkmap'),
        ('invhkmapp','invhkmapp'),
        ('invhkp','invhkp'),
        ('invhls','invhls'),
        ('invhlsearch','invhlsearch'),
        ('invic','invic'),
        ('invicon','invicon'),
        ('invignorecase','invignorecase'),
        ('invim','invim'),
        ('invimc','invimc'),
        ('invimcmdline','invimcmdline'),
        ('invimd','invimd'),
        ('invimdisable','invimdisable'),
        ('invincsearch','invincsearch'),
        ('invinf','invinf'),
        ('invinfercase','invinfercase'),
        ('invinsertmode','invinsertmode'),
        ('invis','invis'),
        ('invjoinspaces','invjoinspaces'),
        ('invjs','invjs'),
        ('invlazyredraw','invlazyredraw'),
        ('invlbr','invlbr'),
        ('invlinebreak','invlinebreak'),
        ('invlisp','invlisp'),
        ('invlist','invlist'),
        ('invloadplugins','invloadplugins'),
        ('invlpl','invlpl'),
        ('invlz','invlz'),
        ('invma','invma'),
        ('invmacatsui','invmacatsui'),
        ('invmagic','invmagic'),
        ('invmh','invmh'),
        ('invml','invml'),
        ('invmod','invmod'),
        ('invmodeline','invmodeline'),
        ('invmodifiable','invmodifiable'),
        ('invmodified','invmodified'),
        ('invmore','invmore'),
        ('invmousef','invmousef'),
        ('invmousefocus','invmousefocus'),
        ('invmousehide','invmousehide'),
        ('invnu','invnu'),
        ('invnumber','invnumber'),
        ('invodev','invodev'),
        ('invopendevice','invopendevice'),
        ('invpaste','invpaste'),
        ('invpi','invpi'),
        ('invpreserveindent','invpreserveindent'),
        ('invpreviewwindow','invpreviewwindow'),
        ('invprompt','invprompt'),
        ('invpvw','invpvw'),
        ('invreadonly','invreadonly'),
        ('invrelativenumber','invrelativenumber'),
        ('invremap','invremap'),
        ('invrestorescreen','invrestorescreen'),
        ('invrevins','invrevins'),
        ('invri','invri'),
        ('invrightleft','invrightleft'),
        ('invrl','invrl'),
        ('invrnu','invrnu'),
        ('invro','invro'),
        ('invrs','invrs'),
        ('invru','invru'),
        ('invruler','invruler'),
        ('invsb','invsb'),
        ('invsc','invsc'),
        ('invscb','invscb'),
        ('invscrollbind','invscrollbind'),
        ('invscs','invscs'),
        ('invsecure','invsecure'),
        ('invsft','invsft'),
        ('invshellslash','invshellslash'),
        ('invshelltemp','invshelltemp'),
        ('invshiftround','invshiftround'),
        ('invshortname','invshortname'),
        ('invshowcmd','invshowcmd'),
        ('invshowfulltag','invshowfulltag'),
        ('invshowmatch','invshowmatch'),
        ('invshowmode','invshowmode'),
        ('invsi','invsi'),
        ('invsm','invsm'),
        ('invsmartcase','invsmartcase'),
        ('invsmartindent','invsmartindent'),
        ('invsmarttab','invsmarttab'),
        ('invsmd','invsmd'),
        ('invsn','invsn'),
        ('invsol','invsol'),
        ('invspell','invspell'),
        ('invsplitbelow','invsplitbelow'),
        ('invsplitright','invsplitright'),
        ('invspr','invspr'),
        ('invsr','invsr'),
        ('invssl','invssl'),
        ('invsta','invsta'),
        ('invstartofline','invstartofline'),
        ('invstmp','invstmp'),
        ('invswapfile','invswapfile'),
        ('invswf','invswf'),
        ('invta','invta'),
        ('invtagbsearch','invtagbsearch'),
        ('invtagrelative','invtagrelative'),
        ('invtagstack','invtagstack'),
        ('invtbi','invtbi'),
        ('invtbidi','invtbidi'),
        ('invtbs','invtbs'),
        ('invtermbidi','invtermbidi'),
        ('invterse','invterse'),
        ('invtextauto','invtextauto'),
        ('invtextmode','invtextmode'),
        ('invtf','invtf'),
        ('invtgst','invtgst'),
        ('invtildeop','invtildeop'),
        ('invtimeout','invtimeout'),
        ('invtitle','invtitle'),
        ('invto','invto'),
        ('invtop','invtop'),
        ('invtr','invtr'),
        ('invttimeout','invttimeout'),
        ('invttybuiltin','invttybuiltin'),
        ('invttyfast','invttyfast'),
        ('invtx','invtx'),
        ('invudf','invudf'),
        ('invundofile','invundofile'),
        ('invvb','invvb'),
        ('invvisualbell','invvisualbell'),
        ('invwa','invwa'),
        ('invwarn','invwarn'),
        ('invwb','invwb'),
        ('invweirdinvert','invweirdinvert'),
        ('invwfh','invwfh'),
        ('invwfw','invwfw'),
        ('invwic','invwic'),
        ('invwildignorecase','invwildignorecase'),
        ('invwildmenu','invwildmenu'),
        ('invwinfixheight','invwinfixheight'),
        ('invwinfixwidth','invwinfixwidth'),
        ('invwiv','invwiv'),
        ('invwmnu','invwmnu'),
        ('invwrap','invwrap'),
        ('invwrapscan','invwrapscan'),
        ('invwrite','invwrite'),
        ('invwriteany','invwriteany'),
        ('invwritebackup','invwritebackup'),
        ('invws','invws'),
        ('is','is'),
        ('isf','isf'),
        ('isfname','isfname'),
        ('isi','isi'),
        ('isident','isident'),
        ('isk','isk'),
        ('iskeyword','iskeyword'),
        ('isp','isp'),
        ('isprint','isprint'),
        ('joinspaces','joinspaces'),
        ('js','js'),
        ('key','key'),
        ('keymap','keymap'),
        ('keymodel','keymodel'),
        ('keywordprg','keywordprg'),
        ('km','km'),
        ('kmp','kmp'),
        ('kp','kp'),
        ('langmap','langmap'),
        ('langmenu','langmenu'),
        ('laststatus','laststatus'),
        ('lazyredraw','lazyredraw'),
        ('lbr','lbr'),
        ('lcs','lcs'),
        ('linebreak','linebreak'),
        ('lines','lines'),
        ('linespace','linespace'),
        ('lisp','lisp'),
        ('lispwords','lispwords'),
        ('list','list'),
        ('listchars','listchars'),
        ('lm','lm'),
        ('lmap','lmap'),
        ('loadplugins','loadplugins'),
        ('lpl','lpl'),
        ('ls','ls'),
        ('lsp','lsp'),
        ('lw','lw'),
        ('lz','lz'),
        ('ma','ma'),
        ('macatsui','macatsui'),
        ('magic','magic'),
        ('makeef','makeef'),
        ('makeprg','makeprg'),
        ('mat','mat'),
        ('matchpairs','matchpairs'),
        ('matchtime','matchtime'),
        ('maxcombine','maxcombine'),
        ('maxfuncdepth','maxfuncdepth'),
        ('maxmapdepth','maxmapdepth'),
        ('maxmem','maxmem'),
        ('maxmempattern','maxmempattern'),
        ('maxmemtot','maxmemtot'),
        ('mco','mco'),
        ('mef','mef'),
        ('menuitems','menuitems'),
        ('mfd','mfd'),
        ('mh','mh'),
        ('mis','mis'),
        ('mkspellmem','mkspellmem'),
        ('ml','ml'),
        ('mls','mls'),
        ('mm','mm'),
        ('mmd','mmd'),
        ('mmp','mmp'),
        ('mmt','mmt'),
        ('mod','mod'),
        ('modeline','modeline'),
        ('modelines','modelines'),
        ('modifiable','modifiable'),
        ('modified','modified'),
        ('more','more'),
        ('mouse','mouse'),
        ('mousef','mousef'),
        ('mousefocus','mousefocus'),
        ('mousehide','mousehide'),
        ('mousem','mousem'),
        ('mousemodel','mousemodel'),
        ('mouses','mouses'),
        ('mouseshape','mouseshape'),
        ('mouset','mouset'),
        ('mousetime','mousetime'),
        ('mp','mp'),
        ('mps','mps'),
        ('msm','msm'),
        ('mzq','mzq'),
        ('mzquantum','mzquantum'),
        ('nf','nf'),
        ('nnoremap','nnoremap'),
        ('noacd','noacd'),
        ('noai','noai'),
        ('noakm','noakm'),
        ('noallowrevins','noallowrevins'),
        ('noaltkeymap','noaltkeymap'),
        ('noanti','noanti'),
        ('noantialias','noantialias'),
        ('noar','noar'),
        ('noarab','noarab'),
        ('noarabic','noarabic'),
        ('noarabicshape','noarabicshape'),
        ('noari','noari'),
        ('noarshape','noarshape'),
        ('noautochdir','noautochdir'),
        ('noautoindent','noautoindent'),
        ('noautoread','noautoread'),
        ('noautowrite','noautowrite'),
        ('noautowriteall','noautowriteall'),
        ('noaw','noaw'),
        ('noawa','noawa'),
        ('nobackup','nobackup'),
        ('noballooneval','noballooneval'),
        ('nobeval','nobeval'),
        ('nobin','nobin'),
        ('nobinary','nobinary'),
        ('nobiosk','nobiosk'),
        ('nobioskey','nobioskey'),
        ('nobk','nobk'),
        ('nobl','nobl'),
        ('nobomb','nobomb'),
        ('nobuflisted','nobuflisted'),
        ('nocf','nocf'),
        ('noci','noci'),
        ('nocin','nocin'),
        ('nocindent','nocindent'),
        ('nocompatible','nocompatible'),
        ('noconfirm','noconfirm'),
        ('noconsk','noconsk'),
        ('noconskey','noconskey'),
        ('nocopyindent','nocopyindent'),
        ('nocp','nocp'),
        ('nocrb','nocrb'),
        ('nocscoperelative','nocscoperelative'),
        ('nocscopetag','nocscopetag'),
        ('nocscopeverbose','nocscopeverbose'),
        ('nocsre','nocsre'),
        ('nocst','nocst'),
        ('nocsverb','nocsverb'),
        ('nocuc','nocuc'),
        ('nocul','nocul'),
        ('nocursorbind','nocursorbind'),
        ('nocursorcolumn','nocursorcolumn'),
        ('nocursorline','nocursorline'),
        ('nodeco','nodeco'),
        ('nodelcombine','nodelcombine'),
        ('nodg','nodg'),
        ('nodiff','nodiff'),
        ('nodigraph','nodigraph'),
        ('noea','noea'),
        ('noeb','noeb'),
        ('noed','noed'),
        ('noedcompatible','noedcompatible'),
        ('noek','noek'),
        ('noendofline','noendofline'),
        ('noeol','noeol'),
        ('noequalalways','noequalalways'),
        ('noerrorbells','noerrorbells'),
        ('noesckeys','noesckeys'),
        ('noet','noet'),
        ('noex','noex'),
        ('noexpandtab','noexpandtab'),
        ('noexrc','noexrc'),
        ('nofen','nofen'),
        ('nofic','nofic'),
        ('nofileignorecase','nofileignorecase'),
        ('nofk','nofk'),
        ('nofkmap','nofkmap'),
        ('nofoldenable','nofoldenable'),
        ('nogd','nogd'),
        ('nogdefault','nogdefault'),
        ('noguipty','noguipty'),
        ('nohid','nohid'),
        ('nohidden','nohidden'),
        ('nohk','nohk'),
        ('nohkmap','nohkmap'),
        ('nohkmapp','nohkmapp'),
        ('nohkp','nohkp'),
        ('nohls','nohls'),
        ('nohlsearch','nohlsearch'),
        ('noic','noic'),
        ('noicon','noicon'),
        ('noignorecase','noignorecase'),
        ('noim','noim'),
        ('noimc','noimc'),
        ('noimcmdline','noimcmdline'),
        ('noimd','noimd'),
        ('noimdisable','noimdisable'),
        ('noincsearch','noincsearch'),
        ('noinf','noinf'),
        ('noinfercase','noinfercase'),
        ('noinsertmode','noinsertmode'),
        ('nois','nois'),
        ('nojoinspaces','nojoinspaces'),
        ('nojs','nojs'),
        ('nolazyredraw','nolazyredraw'),
        ('nolbr','nolbr'),
        ('nolinebreak','nolinebreak'),
        ('nolisp','nolisp'),
        ('nolist','nolist'),
        ('noloadplugins','noloadplugins'),
        ('nolpl','nolpl'),
        ('nolz','nolz'),
        ('noma','noma'),
        ('nomacatsui','nomacatsui'),
        ('nomagic','nomagic'),
        ('nomh','nomh'),
        ('noml','noml'),
        ('nomod','nomod'),
        ('nomodeline','nomodeline'),
        ('nomodifiable','nomodifiable'),
        ('nomodified','nomodified'),
        ('nomore','nomore'),
        ('nomousef','nomousef'),
        ('nomousefocus','nomousefocus'),
        ('nomousehide','nomousehide'),
        ('nonu','nonu'),
        ('nonumber','nonumber'),
        ('noodev','noodev'),
        ('noopendevice','noopendevice'),
        ('nopaste','nopaste'),
        ('nopi','nopi'),
        ('nopreserveindent','nopreserveindent'),
        ('nopreviewwindow','nopreviewwindow'),
        ('noprompt','noprompt'),
        ('nopvw','nopvw'),
        ('noreadonly','noreadonly'),
        ('norelativenumber','norelativenumber'),
        ('noremap','noremap'),
        ('norestorescreen','norestorescreen'),
        ('norevins','norevins'),
        ('nori','nori'),
        ('norightleft','norightleft'),
        ('norl','norl'),
        ('nornu','nornu'),
        ('noro','noro'),
        ('nors','nors'),
        ('noru','noru'),
        ('noruler','noruler'),
        ('nosb','nosb'),
        ('nosc','nosc'),
        ('noscb','noscb'),
        ('noscrollbind','noscrollbind'),
        ('noscs','noscs'),
        ('nosecure','nosecure'),
        ('nosft','nosft'),
        ('noshellslash','noshellslash'),
        ('noshelltemp','noshelltemp'),
        ('noshiftround','noshiftround'),
        ('noshortname','noshortname'),
        ('noshowcmd','noshowcmd'),
        ('noshowfulltag','noshowfulltag'),
        ('noshowmatch','noshowmatch'),
        ('noshowmode','noshowmode'),
        ('nosi','nosi'),
        ('nosm','nosm'),
        ('nosmartcase','nosmartcase'),
        ('nosmartindent','nosmartindent'),
        ('nosmarttab','nosmarttab'),
        ('nosmd','nosmd'),
        ('nosn','nosn'),
        ('nosol','nosol'),
        ('nospell','nospell'),
        ('nosplitbelow','nosplitbelow'),
        ('nosplitright','nosplitright'),
        ('nospr','nospr'),
        ('nosr','nosr'),
        ('nossl','nossl'),
        ('nosta','nosta'),
        ('nostartofline','nostartofline'),
        ('nostmp','nostmp'),
        ('noswapfile','noswapfile'),
        ('noswf','noswf'),
        ('nota','nota'),
        ('notagbsearch','notagbsearch'),
        ('notagrelative','notagrelative'),
        ('notagstack','notagstack'),
        ('notbi','notbi'),
        ('notbidi','notbidi'),
        ('notbs','notbs'),
        ('notermbidi','notermbidi'),
        ('noterse','noterse'),
        ('notextauto','notextauto'),
        ('notextmode','notextmode'),
        ('notf','notf'),
        ('notgst','notgst'),
        ('notildeop','notildeop'),
        ('notimeout','notimeout'),
        ('notitle','notitle'),
        ('noto','noto'),
        ('notop','notop'),
        ('notr','notr'),
        ('nottimeout','nottimeout'),
        ('nottybuiltin','nottybuiltin'),
        ('nottyfast','nottyfast'),
        ('notx','notx'),
        ('noudf','noudf'),
        ('noundofile','noundofile'),
        ('novb','novb'),
        ('novisualbell','novisualbell'),
        ('nowa','nowa'),
        ('nowarn','nowarn'),
        ('nowb','nowb'),
        ('noweirdinvert','noweirdinvert'),
        ('nowfh','nowfh'),
        ('nowfw','nowfw'),
        ('nowic','nowic'),
        ('nowildignorecase','nowildignorecase'),
        ('nowildmenu','nowildmenu'),
        ('nowinfixheight','nowinfixheight'),
        ('nowinfixwidth','nowinfixwidth'),
        ('nowiv','nowiv'),
        ('nowmnu','nowmnu'),
        ('nowrap','nowrap'),
        ('nowrapscan','nowrapscan'),
        ('nowrite','nowrite'),
        ('nowriteany','nowriteany'),
        ('nowritebackup','nowritebackup'),
        ('nows','nows'),
        ('nrformats','nrformats'),
        ('nu','nu'),
        ('number','number'),
        ('numberwidth','numberwidth'),
        ('nuw','nuw'),
        ('odev','odev'),
        ('oft','oft'),
        ('ofu','ofu'),
        ('omnifunc','omnifunc'),
        ('opendevice','opendevice'),
        ('operatorfunc','operatorfunc'),
        ('opfunc','opfunc'),
        ('osfiletype','osfiletype'),
        ('pa','pa'),
        ('para','para'),
        ('paragraphs','paragraphs'),
        ('paste','paste'),
        ('pastetoggle','pastetoggle'),
        ('patchexpr','patchexpr'),
        ('patchmode','patchmode'),
        ('path','path'),
        ('pdev','pdev'),
        ('penc','penc'),
        ('pex','pex'),
        ('pexpr','pexpr'),
        ('pfn','pfn'),
        ('ph','ph'),
        ('pheader','pheader'),
        ('pi','pi'),
        ('pm','pm'),
        ('pmbcs','pmbcs'),
        ('pmbfn','pmbfn'),
        ('popt','popt'),
        ('preserveindent','preserveindent'),
        ('previewheight','previewheight'),
        ('previewwindow','previewwindow'),
        ('printdevice','printdevice'),
        ('printencoding','printencoding'),
        ('printexpr','printexpr'),
        ('printfont','printfont'),
        ('printheader','printheader'),
        ('printmbcharset','printmbcharset'),
        ('printmbfont','printmbfont'),
        ('printoptions','printoptions'),
        ('prompt','prompt'),
        ('pt','pt'),
        ('pumheight','pumheight'),
        ('pvh','pvh'),
        ('pvw','pvw'),
        ('qe','qe'),
        ('quoteescape','quoteescape'),
        ('rdt','rdt'),
        ('re','re'),
        ('readonly','readonly'),
        ('redrawtime','redrawtime'),
        ('regexpengine','regexpengine'),
        ('relativenumber','relativenumber'),
        ('remap','remap'),
        ('report','report'),
        ('restorescreen','restorescreen'),
        ('revins','revins'),
        ('ri','ri'),
        ('rightleft','rightleft'),
        ('rightleftcmd','rightleftcmd'),
        ('rl','rl'),
        ('rlc','rlc'),
        ('rnu','rnu'),
        ('ro','ro'),
        ('rs','rs'),
        ('rtp','rtp'),
        ('ru','ru'),
        ('ruf','ruf'),
        ('ruler','ruler'),
        ('rulerformat','rulerformat'),
        ('runtimepath','runtimepath'),
        ('sb','sb'),
        ('sbo','sbo'),
        ('sbr','sbr'),
        ('sc','sc'),
        ('scb','scb'),
        ('scr','scr'),
        ('scroll','scroll'),
        ('scrollbind','scrollbind'),
        ('scrolljump','scrolljump'),
        ('scrolloff','scrolloff'),
        ('scrollopt','scrollopt'),
        ('scs','scs'),
        ('sect','sect'),
        ('sections','sections'),
        ('secure','secure'),
        ('sel','sel'),
        ('selection','selection'),
        ('selectmode','selectmode'),
        ('sessionoptions','sessionoptions'),
        ('sft','sft'),
        ('sh','sh'),
        ('shcf','shcf'),
        ('shell','shell'),
        ('shellcmdflag','shellcmdflag'),
        ('shellpipe','shellpipe'),
        ('shellquote','shellquote'),
        ('shellredir','shellredir'),
        ('shellslash','shellslash'),
        ('shelltemp','shelltemp'),
        ('shelltype','shelltype'),
        ('shellxescape','shellxescape'),
        ('shellxquote','shellxquote'),
        ('shiftround','shiftround'),
        ('shiftwidth','shiftwidth'),
        ('shm','shm'),
        ('shortmess','shortmess'),
        ('shortname','shortname'),
        ('showbreak','showbreak'),
        ('showcmd','showcmd'),
        ('showfulltag','showfulltag'),
        ('showmatch','showmatch'),
        ('showmode','showmode'),
        ('showtabline','showtabline'),
        ('shq','shq'),
        ('si','si'),
        ('sidescroll','sidescroll'),
        ('sidescrolloff','sidescrolloff'),
        ('siso','siso'),
        ('sj','sj'),
        ('slm','slm'),
        ('sm','sm'),
        ('smartcase','smartcase'),
        ('smartindent','smartindent'),
        ('smarttab','smarttab'),
        ('smc','smc'),
        ('smd','smd'),
        ('sn','sn'),
        ('so','so'),
        ('softtabstop','softtabstop'),
        ('sol','sol'),
        ('sp','sp'),
        ('spc','spc'),
        ('spell','spell'),
        ('spellcapcheck','spellcapcheck'),
        ('spellfile','spellfile'),
        ('spelllang','spelllang'),
        ('spellsuggest','spellsuggest'),
        ('spf','spf'),
        ('spl','spl'),
        ('splitbelow','splitbelow'),
        ('splitright','splitright'),
        ('spr','spr'),
        ('sps','sps'),
        ('sr','sr'),
        ('srr','srr'),
        ('ss','ss'),
        ('ssl','ssl'),
        ('ssop','ssop'),
        ('st','st'),
        ('sta','sta'),
        ('stal','stal'),
        ('startofline','startofline'),
        ('statusline','statusline'),
        ('stl','stl'),
        ('stmp','stmp'),
        ('sts','sts'),
        ('su','su'),
        ('sua','sua'),
        ('suffixes','suffixes'),
        ('suffixesadd','suffixesadd'),
        ('sw','sw'),
        ('swapfile','swapfile'),
        ('swapsync','swapsync'),
        ('swb','swb'),
        ('swf','swf'),
        ('switchbuf','switchbuf'),
        ('sws','sws'),
        ('sxe','sxe'),
        ('sxq','sxq'),
        ('syn','syn'),
        ('synmaxcol','synmaxcol'),
        ('syntax','syntax'),
        ('t_AB','t_AB'),
        ('t_AF','t_AF'),
        ('t_AL','t_AL'),
        ('t_CS','t_CS'),
        ('t_CV','t_CV'),
        ('t_Ce','t_Ce'),
        ('t_Co','t_Co'),
        ('t_Cs','t_Cs'),
        ('t_DL','t_DL'),
        ('t_EI','t_EI'),
        ('t_F1','t_F1'),
        ('t_F2','t_F2'),
        ('t_F3','t_F3'),
        ('t_F4','t_F4'),
        ('t_F5','t_F5'),
        ('t_F6','t_F6'),
        ('t_F7','t_F7'),
        ('t_F8','t_F8'),
        ('t_F9','t_F9'),
        ('t_IE','t_IE'),
        ('t_IS','t_IS'),
        ('t_K1','t_K1'),
        ('t_K3','t_K3'),
        ('t_K4','t_K4'),
        ('t_K5','t_K5'),
        ('t_K6','t_K6'),
        ('t_K7','t_K7'),
        ('t_K8','t_K8'),
        ('t_K9','t_K9'),
        ('t_KA','t_KA'),
        ('t_KB','t_KB'),
        ('t_KC','t_KC'),
        ('t_KD','t_KD'),
        ('t_KE','t_KE'),
        ('t_KF','t_KF'),
        ('t_KG','t_KG'),
        ('t_KH','t_KH'),
        ('t_KI','t_KI'),
        ('t_KJ','t_KJ'),
        ('t_KK','t_KK'),
        ('t_KL','t_KL'),
        ('t_RI','t_RI'),
        ('t_RV','t_RV'),
        ('t_SI','t_SI'),
        ('t_Sb','t_Sb'),
        ('t_Sf','t_Sf'),
        ('t_WP','t_WP'),
        ('t_WS','t_WS'),
        ('t_ZH','t_ZH'),
        ('t_ZR','t_ZR'),
        ('t_al','t_al'),
        ('t_bc','t_bc'),
        ('t_cd','t_cd'),
        ('t_ce','t_ce'),
        ('t_cl','t_cl'),
        ('t_cm','t_cm'),
        ('t_cs','t_cs'),
        ('t_da','t_da'),
        ('t_db','t_db'),
        ('t_dl','t_dl'),
        ('t_fs','t_fs'),
        ('t_k1','t_k1'),
        ('t_k2','t_k2'),
        ('t_k3','t_k3'),
        ('t_k4','t_k4'),
        ('t_k5','t_k5'),
        ('t_k6','t_k6'),
        ('t_k7','t_k7'),
        ('t_k8','t_k8'),
        ('t_k9','t_k9'),
        ('t_kB','t_kB'),
        ('t_kD','t_kD'),
        ('t_kI','t_kI'),
        ('t_kN','t_kN'),
        ('t_kP','t_kP'),
        ('t_kb','t_kb'),
        ('t_kd','t_kd'),
        ('t_ke','t_ke'),
        ('t_kh','t_kh'),
        ('t_kl','t_kl'),
        ('t_kr','t_kr'),
        ('t_ks','t_ks'),
        ('t_ku','t_ku'),
        ('t_le','t_le'),
        ('t_mb','t_mb'),
        ('t_md','t_md'),
        ('t_me','t_me'),
        ('t_mr','t_mr'),
        ('t_ms','t_ms'),
        ('t_nd','t_nd'),
        ('t_op','t_op'),
        ('t_se','t_se'),
        ('t_so','t_so'),
        ('t_sr','t_sr'),
        ('t_te','t_te'),
        ('t_ti','t_ti'),
        ('t_ts','t_ts'),
        ('t_u7','t_u7'),
        ('t_ue','t_ue'),
        ('t_us','t_us'),
        ('t_ut','t_ut'),
        ('t_vb','t_vb'),
        ('t_ve','t_ve'),
        ('t_vi','t_vi'),
        ('t_vs','t_vs'),
        ('t_xs','t_xs'),
        ('ta','ta'),
        ('tabline','tabline'),
        ('tabpagemax','tabpagemax'),
        ('tabstop','tabstop'),
        ('tag','tag'),
        ('tagbsearch','tagbsearch'),
        ('taglength','taglength'),
        ('tagrelative','tagrelative'),
        ('tags','tags'),
        ('tagstack','tagstack'),
        ('tal','tal'),
        ('tb','tb'),
        ('tbi','tbi'),
        ('tbidi','tbidi'),
        ('tbis','tbis'),
        ('tbs','tbs'),
        ('tenc','tenc'),
        ('term','term'),
        ('termbidi','termbidi'),
        ('termencoding','termencoding'),
        ('terse','terse'),
        ('textauto','textauto'),
        ('textmode','textmode'),
        ('textwidth','textwidth'),
        ('tf','tf'),
        ('tgst','tgst'),
        ('thesaurus','thesaurus'),
        ('tildeop','tildeop'),
        ('timeout','timeout'),
        ('timeoutlen','timeoutlen'),
        ('title','title'),
        ('titlelen','titlelen'),
        ('titleold','titleold'),
        ('titlestring','titlestring'),
        ('tl','tl'),
        ('tm','tm'),
        ('to','to'),
        ('toolbar','toolbar'),
        ('toolbariconsize','toolbariconsize'),
        ('top','top'),
        ('tpm','tpm'),
        ('tr','tr'),
        ('ts','ts'),
        ('tsl','tsl'),
        ('tsr','tsr'),
        ('ttimeout','ttimeout'),
        ('ttimeoutlen','ttimeoutlen'),
        ('ttm','ttm'),
        ('tty','tty'),
        ('ttybuiltin','ttybuiltin'),
        ('ttyfast','ttyfast'),
        ('ttym','ttym'),
        ('ttymouse','ttymouse'),
        ('ttyscroll','ttyscroll'),
        ('ttytype','ttytype'),
        ('tw','tw'),
        ('tx','tx'),
        ('uc','uc'),
        ('udf','udf'),
        ('udir','udir'),
        ('ul','ul'),
        ('undodir','undodir'),
        ('undofile','undofile'),
        ('undolevels','undolevels'),
        ('undoreload','undoreload'),
        ('updatecount','updatecount'),
        ('updatetime','updatetime'),
        ('ur','ur'),
        ('ut','ut'),
        ('vb','vb'),
        ('vbs','vbs'),
        ('vdir','vdir'),
        ('ve','ve'),
        ('verbose','verbose'),
        ('verbosefile','verbosefile'),
        ('vfile','vfile'),
        ('vi','vi'),
        ('viewdir','viewdir'),
        ('viewoptions','viewoptions'),
        ('viminfo','viminfo'),
        ('virtualedit','virtualedit'),
        ('visualbell','visualbell'),
        ('vnoremap','vnoremap'),
        ('vop','vop'),
        ('wa','wa'),
        ('wak','wak'),
        ('warn','warn'),
        ('wb','wb'),
        ('wc','wc'),
        ('wcm','wcm'),
        ('wd','wd'),
        ('weirdinvert','weirdinvert'),
        ('wfh','wfh'),
        ('wfw','wfw'),
        ('wh','wh'),
        ('whichwrap','whichwrap'),
        ('wi','wi'),
        ('wic','wic'),
        ('wig','wig'),
        ('wildchar','wildchar'),
        ('wildcharm','wildcharm'),
        ('wildignore','wildignore'),
        ('wildignorecase','wildignorecase'),
        ('wildmenu','wildmenu'),
        ('wildmode','wildmode'),
        ('wildoptions','wildoptions'),
        ('wim','wim'),
        ('winaltkeys','winaltkeys'),
        ('window','window'),
        ('winfixheight','winfixheight'),
        ('winfixwidth','winfixwidth'),
        ('winheight','winheight'),
        ('winminheight','winminheight'),
        ('winminwidth','winminwidth'),
        ('winwidth','winwidth'),
        ('wiv','wiv'),
        ('wiw','wiw'),
        ('wm','wm'),
        ('wmh','wmh'),
        ('wmnu','wmnu'),
        ('wmw','wmw'),
        ('wop','wop'),
        ('wrap','wrap'),
        ('wrapmargin','wrapmargin'),
        ('wrapscan','wrapscan'),
        ('write','write'),
        ('writeany','writeany'),
        ('writebackup','writebackup'),
        ('writedelay','writedelay'),
        ('ws','ws'),
        ('ww','ww'),
    )
    return var
option = _getoption()


# Money Tracker

Welcome to Money Tracker, your personal finance management solution.

## Overview

Money Tracker is a Flask-based web application designed to help you manage your personal finances effectively. With an intuitive interface and powerful features, you can easily track expenses, monitor income, and gain insights into your spending patterns.

## Key Features

- **Transaction Management**: Record and categorize your expenses and income
- **Financial Dashboard**: Visualize your financial data with interactive charts
- **Category Management**: Organize transactions with customizable categories
- **Salary Tracking**: Monitor your income over time
- **Data Export**: Download your financial data for external analysis
- **Responsive Design**: Access your finances from any device

## Getting Started

New to Money Tracker? Here's how to get started:

1. [Create an account](user-guide.md#registration) or log in
2. [Add your first transaction](user-guide.md#adding-transactions)
3. [Explore your dashboard](user-guide.md#dashboard-overview)
4. [Set up categories](user-guide.md#managing-categories) to organize your finances

## Learn More

For more detailed information, check out these resources:

- [User Guide](user-guide.md) - Complete instructions for using Money Tracker
- [Technical Documentation](technical-docs.md) - Details about the application architecture
- [FAQ](faq.md) - Answers to common questions

## Support

Need help? Check the [FAQ](faq.md) or create an issue in our project repository.

---

[Return to Wiki Index](index.md)

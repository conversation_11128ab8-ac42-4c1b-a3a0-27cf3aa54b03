# Money Tracker Wiki

[Home](index.md) | [User Guide](user-guide.md) | [Technical Docs](technical-docs.md) | [Development Guide](development-guide.md) | [Deployment Guide](deployment-guide.md) | [FAQ](faq.md)

Welcome to the Money Tracker Wiki! This is your comprehensive guide to understanding, using, and contributing to the Money Tracker application.

## Quick Links

- [User Guide](user-guide.md)
- [Technical Documentation](technical-docs.md)
- [Development Guide](development-guide.md)
- [Deployment Guide](deployment-guide.md)
- [FAQ](faq.md)

## About Money Tracker

Money Tracker is a Flask-based web application designed to help users manage their personal finances. It provides tools for tracking expenses, income, and visualizing spending patterns. The application uses MongoDB for data storage and offers a responsive, user-friendly interface built with Bootstrap 5.

### Key Features

- **Enhanced Security Framework**:
  - Two-factor authentication (2FA)
  - Session management and tracking
  - Security logs and monitoring
  - Account lockout protection
  - Strong password policies
  - Password reset functionality
- **Transaction Management**: Track both expenses (debits) and income (credits)
- **Financial Dashboard**: Visualize spending patterns and financial summaries
- **Category Management**: Organize transactions with customizable categories
- **Salary Tracking**: Monitor income over time
- **Data Export**: Download transaction data for external analysis
- **Responsive Design**: Mobile-friendly interface

## Getting Started

New to Money Tracker? Check out these resources:

1. [Installation Guide](user-guide.md#installation)
2. [Quick Start Tutorial](user-guide.md#getting-started)
3. [Dashboard Overview](user-guide.md#dashboard-overview)

## For Developers

If you're looking to contribute to Money Tracker or understand its architecture:

1. [Project Structure](development-guide.md#project-structure)
2. [Adding New Features](development-guide.md#adding-new-features)
3. [Contributing Guidelines](development-guide.md#contributing)

## Recent Updates

- Added comprehensive security features documentation
- Detailed guides for two-factor authentication setup and usage
- Documentation for session management and security logs
- Initial Wiki creation
- Comprehensive documentation of features and functionality
- Technical architecture documentation
- Deployment guides for Render.com

## Support

If you encounter any issues or have questions not covered in this Wiki, please:

1. Check the [FAQ](faq.md) for common questions
2. Search for similar issues in the project's issue tracker
3. Create a new issue if your problem hasn't been addressed
